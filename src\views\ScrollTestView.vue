<template>
  <div class="scroll-test-container">
    <van-nav-bar
      title="滚动测试"
      left-text="返回"
      left-arrow
      @click-left="goBack"
      fixed
      placeholder
    />

    <!-- 消息列表 -->
    <div class="message-list" ref="messageListRef">
      <div class="messages">
        <div
          v-for="(message, index) in messages"
          :key="index"
          :class="[
            'message-item',
            { 'own-message': message.isOwn }
          ]"
        >
          <div class="message-avatar">
            <van-image
              v-if="!message.isOwn"
              src="https://via.placeholder.com/40"
              class="avatar"
              round
              width="40"
              height="40"
            />
          </div>

          <div class="message-content">
            <div
              :class="[
                'message-bubble',
                message.isOwn ? 'message-bubble--self' : 'message-bubble--other'
              ]"
            >
              {{ message.content }}
            </div>
          </div>

          <div class="message-avatar">
            <van-image
              v-if="message.isOwn"
              src="https://via.placeholder.com/40"
              class="avatar"
              round
              width="40"
              height="40"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <div class="input-container">
        <van-field
          v-model="inputMessage"
          placeholder="输入消息..."
          @keyup.enter="sendMessage"
        />
        <van-button type="primary" @click="sendMessage" :loading="sending">
          发送
        </van-button>
      </div>
      
      <!-- 测试按钮 -->
      <div class="test-buttons">
        <van-button size="small" @click="addMessage(false)">添加他人消息</van-button>
        <van-button size="small" @click="addMessage(true)">添加自己消息</van-button>
        <van-button size="small" @click="scrollToBottom">滚动到底部</van-button>
        <van-button size="small" @click="clearMessages">清空消息</van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const messageListRef = ref<HTMLElement>()
const inputMessage = ref('')
const sending = ref(false)

interface TestMessage {
  content: string
  isOwn: boolean
  timestamp: Date
}

const messages = ref<TestMessage[]>([
  { content: '你好！这是一条测试消息', isOwn: false, timestamp: new Date() },
  { content: '你好！我收到了', isOwn: true, timestamp: new Date() },
  { content: '这是一个滚动测试页面', isOwn: false, timestamp: new Date() },
  { content: '我们来测试一下滚动功能', isOwn: true, timestamp: new Date() },
])

// 返回上一页
const goBack = () => {
  router.back()
}

// 精确计算滚动位置
const calculatePreciseScrollPosition = () => {
  if (!messageListRef.value) return 0

  const messageList = messageListRef.value
  const messagesContainer = messageList.querySelector('.messages') as HTMLElement
  
  if (!messagesContainer) return 0

  const containerHeight = messageList.clientHeight
  const contentHeight = messagesContainer.scrollHeight
  const inputArea = document.querySelector('.input-area') as HTMLElement
  const inputHeight = inputArea ? inputArea.getBoundingClientRect().height : 120
  const navbar = document.querySelector('.van-nav-bar') as HTMLElement
  const navbarHeight = navbar ? navbar.getBoundingClientRect().height : 46
  const availableHeight = window.innerHeight - navbarHeight - inputHeight
  const targetScrollTop = Math.max(0, contentHeight - availableHeight)

  console.log('滚动计算:', {
    containerHeight,
    contentHeight,
    availableHeight,
    inputHeight,
    navbarHeight,
    targetScrollTop,
    windowHeight: window.innerHeight
  })

  return targetScrollTop
}

// 滚动到底部
const scrollToBottom = async () => {
  if (!messageListRef.value) return

  const targetScrollTop = calculatePreciseScrollPosition()
  
  console.log(`滚动到底部: 目标位置 ${targetScrollTop}`)

  messageListRef.value.scrollTo({
    top: targetScrollTop,
    behavior: 'smooth'
  })

  // 验证滚动
  setTimeout(() => {
    if (messageListRef.value) {
      const actualScrollTop = messageListRef.value.scrollTop
      console.log(`滚动验证: 实际位置 ${actualScrollTop}, 目标位置 ${targetScrollTop}`)
      
      if (Math.abs(actualScrollTop - targetScrollTop) > 5) {
        console.log('滚动位置不准确，进行微调')
        messageListRef.value.scrollTop = targetScrollTop
      }
    }
  }, 300)
}

// 发送消息后的滚动
const scrollAfterSendMessage = async () => {
  if (!messageListRef.value) return

  await nextTick()
  await new Promise(resolve => setTimeout(resolve, 50))
  
  const messagesContainer = messageListRef.value.querySelector('.messages') as HTMLElement
  if (messagesContainer) {
    const maxScrollTop = messagesContainer.scrollHeight - messageListRef.value.clientHeight
    messageListRef.value.scrollTop = maxScrollTop
    
    console.log('发送消息后滚动:', {
      scrollHeight: messagesContainer.scrollHeight,
      clientHeight: messageListRef.value.clientHeight,
      scrollTop: messageListRef.value.scrollTop,
      maxScrollTop
    })
  }
}

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim() || sending.value) return

  sending.value = true
  const content = inputMessage.value.trim()
  inputMessage.value = ''

  // 添加消息
  messages.value.push({
    content,
    isOwn: true,
    timestamp: new Date()
  })

  // 滚动到底部
  await scrollAfterSendMessage()
  
  sending.value = false
}

// 添加测试消息
const addMessage = async (isOwn: boolean) => {
  const content = isOwn 
    ? `我的消息 ${messages.value.length + 1}` 
    : `他人消息 ${messages.value.length + 1}`
  
  messages.value.push({
    content,
    isOwn,
    timestamp: new Date()
  })

  if (isOwn) {
    await scrollAfterSendMessage()
  } else {
    await scrollToBottom()
  }
}

// 清空消息
const clearMessages = () => {
  messages.value = []
}
</script>

<style scoped>
.scroll-test-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  padding-bottom: 20px;
  height: calc(100vh - 46px - 120px);
  max-height: calc(100vh - 46px - 120px);
}

.messages {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-item {
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.message-item.own-message {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 8px;
  word-wrap: break-word;
  line-height: 1.4;
}

.message-bubble--self {
  background: #07C160;
  color: white;
  margin-left: auto;
}

.message-bubble--other {
  background: white;
  color: #333;
  margin-right: auto;
}

.input-area {
  background: white;
  padding: 12px;
  border-top: 1px solid #eee;
}

.input-container {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
}

.test-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
</style>
