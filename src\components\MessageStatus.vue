<template>
  <div class="message-status-container">
    <!-- 时间戳 -->
    <span v-if="showTime" class="message-time">
      {{ formattedTime }}
    </span>
    
    <!-- 状态指示器 -->
    <div v-if="isOwnMessage" class="status-indicators">
      <!-- 发送中 -->
      <van-loading 
        v-if="status === 'sending'" 
        size="12" 
        color="var(--wechat-text-tertiary)"
        class="status-icon"
      />
      
      <!-- 发送失败 -->
      <van-icon 
        v-else-if="status === 'failed'"
        name="warning-o"
        size="12"
        color="var(--wechat-danger)"
        class="status-icon failed"
        @click="$emit('retry')"
      />
      
      <!-- 已发送 -->
      <van-icon 
        v-else-if="status === 'sent'"
        name="success"
        size="12"
        color="var(--wechat-text-tertiary)"
        class="status-icon"
      />
      
      <!-- 已送达 -->
      <van-icon 
        v-else-if="status === 'delivered'"
        name="success"
        size="12"
        color="var(--wechat-text-secondary)"
        class="status-icon"
      />
      
      <!-- 已读 -->
      <div v-else-if="status === 'read'" class="read-indicator">
        <van-icon 
          name="success"
          size="12"
          color="var(--wechat-primary)"
          class="status-icon read"
        />
        <span class="read-text">已读</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { TimestampManager } from '@/utils/messageStatus'
import type { MessageStatus } from '@/utils/messageStatus'

interface Props {
  status: MessageStatus
  timestamp: string
  isOwnMessage?: boolean
  showTime?: boolean
  readAt?: string
}

const props = withDefaults(defineProps<Props>(), {
  isOwnMessage: false,
  showTime: true
})

const emit = defineEmits<{
  retry: []
}>()

// 格式化时间显示
const formattedTime = computed(() => {
  return TimestampManager.formatMessageTime(props.timestamp)
})

// 状态文本
const statusText = computed(() => {
  switch (props.status) {
    case 'sending':
      return '发送中'
    case 'sent':
      return '已发送'
    case 'delivered':
      return '已送达'
    case 'read':
      return '已读'
    case 'failed':
      return '发送失败'
    default:
      return ''
  }
})
</script>

<style scoped>
.message-status-container {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 4px;
  font-size: 10px;
  color: var(--wechat-text-tertiary);
  justify-content: flex-end;
}

.message-time {
  opacity: 0.7;
  white-space: nowrap;
}

.status-indicators {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-icon {
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.status-icon.failed {
  cursor: pointer;
  animation: shake 0.5s ease-in-out;
}

.status-icon.failed:hover {
  transform: scale(1.1);
}

.status-icon.read {
  animation: readPulse 0.3s ease-out;
}

.read-indicator {
  display: flex;
  align-items: center;
  gap: 2px;
}

.read-text {
  font-size: 9px;
  color: var(--wechat-primary);
  font-weight: 500;
}

/* 动画效果 */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

@keyframes readPulse {
  0% { 
    transform: scale(1);
    opacity: 0.5;
  }
  50% { 
    transform: scale(1.2);
    opacity: 1;
  }
  100% { 
    transform: scale(1);
    opacity: 1;
  }
}

/* 左对齐（其他人的消息） */
.message-status-container.other-message {
  justify-content: flex-start;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .message-status-container {
    font-size: 9px;
    gap: 4px;
  }
  
  .read-text {
    font-size: 8px;
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .message-time {
    color: var(--wechat-text-tertiary);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .message-time {
    opacity: 1;
    font-weight: 500;
  }
  
  .read-text {
    font-weight: 600;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .status-icon,
  .status-icon.failed,
  .status-icon.read {
    animation: none;
    transition: none;
  }
  
  .status-icon.failed:hover {
    transform: none;
  }
}
</style>
