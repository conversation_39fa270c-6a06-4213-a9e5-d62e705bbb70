/**
 * 键盘适配工具类
 * 处理移动端键盘弹出/收起时的页面布局调整
 */

export interface KeyboardState {
  isOpen: boolean
  height: number
  originalViewportHeight: number
  currentViewportHeight: number
}

export interface KeyboardOptions {
  threshold?: number // 键盘检测阈值，默认150px
  debounceTime?: number // 防抖时间，默认100ms
  enableAutoScroll?: boolean // 是否启用自动滚动，默认true
}

export class KeyboardAdapter {
  private static instance: KeyboardAdapter | null = null
  
  private originalViewportHeight: number = 0
  private currentViewportHeight: number = 0
  private isKeyboardOpen: boolean = false
  private keyboardHeight: number = 0
  
  private options: Required<KeyboardOptions>
  private callbacks: Array<(state: KeyboardState) => void> = []
  private resizeTimer: NodeJS.Timeout | null = null
  private focusTimer: NodeJS.Timeout | null = null
  
  private constructor(options: KeyboardOptions = {}) {
    this.options = {
      threshold: 150,
      debounceTime: 100,
      enableAutoScroll: true,
      ...options
    }
    
    this.originalViewportHeight = window.innerHeight
    this.currentViewportHeight = window.innerHeight
    
    this.bindEvents()
  }

  static getInstance(options?: KeyboardOptions): KeyboardAdapter {
    if (!this.instance) {
      this.instance = new KeyboardAdapter(options)
    }
    return this.instance
  }

  /**
   * 绑定事件监听器
   */
  private bindEvents(): void {
    // 监听视口大小变化
    window.addEventListener('resize', this.handleResize.bind(this))
    
    // 监听输入框焦点事件
    document.addEventListener('focusin', this.handleFocusIn.bind(this))
    document.addEventListener('focusout', this.handleFocusOut.bind(this))
    
    // 监听视觉视口变化（支持Visual Viewport API的浏览器）
    if ('visualViewport' in window) {
      window.visualViewport?.addEventListener('resize', this.handleVisualViewportResize.bind(this))
    }
  }

  /**
   * 处理窗口大小变化
   */
  private handleResize(): void {
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer)
    }

    this.resizeTimer = setTimeout(() => {
      this.updateViewportState()
    }, this.options.debounceTime)
  }

  /**
   * 处理输入框获得焦点
   */
  private handleFocusIn(event: FocusEvent): void {
    const target = event.target as HTMLElement
    
    if (this.isInputElement(target)) {
      // 延迟检测，等待键盘完全弹出
      if (this.focusTimer) {
        clearTimeout(this.focusTimer)
      }
      
      this.focusTimer = setTimeout(() => {
        this.updateViewportState()
        
        // 如果启用自动滚动，将输入框滚动到可视区域
        if (this.options.enableAutoScroll) {
          this.scrollInputIntoView(target)
        }
      }, 300)
    }
  }

  /**
   * 处理输入框失去焦点
   */
  private handleFocusOut(): void {
    // 延迟检测，等待键盘完全收起
    if (this.focusTimer) {
      clearTimeout(this.focusTimer)
    }
    
    this.focusTimer = setTimeout(() => {
      this.updateViewportState()
    }, 300)
  }

  /**
   * 处理视觉视口变化（现代浏览器）
   */
  private handleVisualViewportResize(): void {
    if ('visualViewport' in window && window.visualViewport) {
      const visualHeight = window.visualViewport.height
      const windowHeight = window.innerHeight
      
      this.currentViewportHeight = visualHeight
      this.keyboardHeight = windowHeight - visualHeight
      this.isKeyboardOpen = this.keyboardHeight > this.options.threshold
      
      this.notifyStateChange()
    }
  }

  /**
   * 更新视口状态
   */
  private updateViewportState(): void {
    this.currentViewportHeight = window.innerHeight
    const heightDifference = this.originalViewportHeight - this.currentViewportHeight
    
    const wasKeyboardOpen = this.isKeyboardOpen
    this.isKeyboardOpen = heightDifference > this.options.threshold
    this.keyboardHeight = this.isKeyboardOpen ? heightDifference : 0
    
    // 只有状态发生变化时才通知
    if (wasKeyboardOpen !== this.isKeyboardOpen) {
      console.log(`键盘状态变化: ${this.isKeyboardOpen ? '弹出' : '收起'}`, {
        originalHeight: this.originalViewportHeight,
        currentHeight: this.currentViewportHeight,
        keyboardHeight: this.keyboardHeight
      })
      
      this.notifyStateChange()
    }
  }

  /**
   * 判断是否为输入元素
   */
  private isInputElement(element: HTMLElement): boolean {
    const inputTypes = ['INPUT', 'TEXTAREA']
    const inputTypeAttr = element.getAttribute('type')
    
    return inputTypes.includes(element.tagName) || 
           (element.tagName === 'INPUT' && inputTypeAttr !== 'button' && inputTypeAttr !== 'submit')
  }

  /**
   * 将输入框滚动到可视区域
   */
  private scrollInputIntoView(inputElement: HTMLElement): void {
    try {
      // 获取输入框位置
      const rect = inputElement.getBoundingClientRect()
      const viewportHeight = this.currentViewportHeight
      
      // 如果输入框被键盘遮挡，滚动页面
      if (rect.bottom > viewportHeight) {
        const scrollOffset = rect.bottom - viewportHeight + 20 // 20px缓冲
        window.scrollBy({
          top: scrollOffset,
          behavior: 'smooth'
        })
      }
    } catch (error) {
      console.warn('滚动输入框到可视区域失败:', error)
    }
  }

  /**
   * 通知状态变化
   */
  private notifyStateChange(): void {
    const state: KeyboardState = {
      isOpen: this.isKeyboardOpen,
      height: this.keyboardHeight,
      originalViewportHeight: this.originalViewportHeight,
      currentViewportHeight: this.currentViewportHeight
    }

    this.callbacks.forEach(callback => {
      try {
        callback(state)
      } catch (error) {
        console.error('键盘状态回调执行失败:', error)
      }
    })
  }

  /**
   * 添加状态变化监听器
   */
  onStateChange(callback: (state: KeyboardState) => void): () => void {
    this.callbacks.push(callback)
    
    // 返回取消监听的函数
    return () => {
      const index = this.callbacks.indexOf(callback)
      if (index > -1) {
        this.callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 获取当前键盘状态
   */
  getState(): KeyboardState {
    return {
      isOpen: this.isKeyboardOpen,
      height: this.keyboardHeight,
      originalViewportHeight: this.originalViewportHeight,
      currentViewportHeight: this.currentViewportHeight
    }
  }

  /**
   * 手动触发键盘状态检测
   */
  checkState(): void {
    this.updateViewportState()
  }

  /**
   * 重置原始视口高度（用于屏幕旋转等场景）
   */
  resetOriginalHeight(): void {
    this.originalViewportHeight = window.innerHeight
    this.updateViewportState()
  }

  /**
   * 销毁实例，清理事件监听器
   */
  destroy(): void {
    // 清理定时器
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer)
    }
    if (this.focusTimer) {
      clearTimeout(this.focusTimer)
    }

    // 移除事件监听器
    window.removeEventListener('resize', this.handleResize.bind(this))
    document.removeEventListener('focusin', this.handleFocusIn.bind(this))
    document.removeEventListener('focusout', this.handleFocusOut.bind(this))
    
    if ('visualViewport' in window) {
      window.visualViewport?.removeEventListener('resize', this.handleVisualViewportResize.bind(this))
    }

    // 清空回调
    this.callbacks = []
    
    // 重置单例
    KeyboardAdapter.instance = null
  }
}

/**
 * 便捷函数：创建键盘适配器实例
 */
export const createKeyboardAdapter = (options?: KeyboardOptions) => {
  return KeyboardAdapter.getInstance(options)
}

/**
 * 便捷函数：获取键盘状态
 */
export const getKeyboardState = (): KeyboardState => {
  return KeyboardAdapter.getInstance().getState()
}

// 在开发环境下暴露到全局
if (import.meta.env.DEV) {
  (window as any).KeyboardAdapter = KeyboardAdapter
  (window as any).getKeyboardState = getKeyboardState
}
