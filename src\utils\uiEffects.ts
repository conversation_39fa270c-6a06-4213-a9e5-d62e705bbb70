/**
 * 现代UI效果工具
 * 处理毛玻璃效果、动态阴影、交互动画等
 */

export interface GlassEffectOptions {
  blur: number
  opacity: number
  borderOpacity: number
  shadowIntensity: number
}

export interface ShadowConfig {
  elevation: 'subtle' | 'soft' | 'elevated' | 'floating'
  color?: string
  animated?: boolean
}

export class UIEffectsManager {
  private static instance: UIEffectsManager
  private observers: IntersectionObserver[] = []
  private animationFrameId: number | null = null
  
  static getInstance(): UIEffectsManager {
    if (!UIEffectsManager.instance) {
      UIEffectsManager.instance = new UIEffectsManager()
    }
    return UIEffectsManager.instance
  }
  
  /**
   * 应用毛玻璃效果
   */
  applyGlassEffect(element: HTMLElement, options: Partial<GlassEffectOptions> = {}) {
    const config: GlassEffectOptions = {
      blur: 20,
      opacity: 0.8,
      borderOpacity: 0.2,
      shadowIntensity: 0.1,
      ...options
    }
    
    element.style.backdropFilter = `blur(${config.blur}px)`
    element.style.webkitBackdropFilter = `blur(${config.blur}px)`
    element.style.background = `rgba(255, 255, 255, ${config.opacity})`
    element.style.border = `1px solid rgba(255, 255, 255, ${config.borderOpacity})`
    element.style.boxShadow = `0 8px 32px rgba(0, 0, 0, ${config.shadowIntensity})`
  }
  
  /**
   * 应用动态阴影
   */
  applyShadow(element: HTMLElement, config: ShadowConfig) {
    const shadows = {
      subtle: '0 0.8px 2px rgba(0, 0, 0, 0.08), 0 1.6px 4px rgba(0, 0, 0, 0.04)',
      soft: '0 2px 8px rgba(0, 0, 0, 0.06), 0 4px 16px rgba(0, 0, 0, 0.03)',
      elevated: '0 4px 12px rgba(0, 0, 0, 0.08), 0 8px 24px rgba(0, 0, 0, 0.04)',
      floating: '0 8px 24px rgba(0, 0, 0, 0.12), 0 16px 48px rgba(0, 0, 0, 0.06)'
    }
    
    element.style.boxShadow = shadows[config.elevation]
    
    if (config.animated) {
      element.style.transition = 'box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
    }
  }
  
  /**
   * 添加悬停效果
   */
  addHoverEffect(element: HTMLElement, options: {
    scale?: number
    shadow?: ShadowConfig
    transform?: string
  } = {}) {
    const { scale = 1.02, shadow, transform } = options
    
    element.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
    element.style.cursor = 'pointer'
    
    const handleMouseEnter = () => {
      element.style.transform = transform || `scale(${scale}) translateY(-2px)`
      if (shadow) {
        this.applyShadow(element, shadow)
      }
    }
    
    const handleMouseLeave = () => {
      element.style.transform = 'scale(1) translateY(0)'
      if (shadow) {
        this.applyShadow(element, { elevation: 'subtle' })
      }
    }
    
    element.addEventListener('mouseenter', handleMouseEnter)
    element.addEventListener('mouseleave', handleMouseLeave)
    
    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter)
      element.removeEventListener('mouseleave', handleMouseLeave)
    }
  }
  
  /**
   * 添加点击波纹效果
   */
  addRippleEffect(element: HTMLElement, color = 'rgba(7, 193, 96, 0.3)') {
    element.style.position = 'relative'
    element.style.overflow = 'hidden'
    
    const handleClick = (event: MouseEvent) => {
      const rect = element.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      
      const ripple = document.createElement('div')
      ripple.style.position = 'absolute'
      ripple.style.borderRadius = '50%'
      ripple.style.background = color
      ripple.style.transform = 'scale(0)'
      ripple.style.animation = 'ripple 0.6s linear'
      ripple.style.left = `${x - 10}px`
      ripple.style.top = `${y - 10}px`
      ripple.style.width = '20px'
      ripple.style.height = '20px'
      ripple.style.pointerEvents = 'none'
      
      element.appendChild(ripple)
      
      setTimeout(() => {
        ripple.remove()
      }, 600)
    }
    
    element.addEventListener('click', handleClick)
    
    // 添加CSS动画
    if (!document.querySelector('#ripple-styles')) {
      const style = document.createElement('style')
      style.id = 'ripple-styles'
      style.textContent = `
        @keyframes ripple {
          to {
            transform: scale(4);
            opacity: 0;
          }
        }
      `
      document.head.appendChild(style)
    }
    
    return () => {
      element.removeEventListener('click', handleClick)
    }
  }
  
  /**
   * 添加视差滚动效果
   */
  addParallaxEffect(element: HTMLElement, intensity = 0.5) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.startParallax(element, intensity)
        } else {
          this.stopParallax()
        }
      })
    })
    
    observer.observe(element)
    this.observers.push(observer)
  }
  
  private startParallax(element: HTMLElement, intensity: number) {
    const updateParallax = () => {
      const scrolled = window.pageYOffset
      const rect = element.getBoundingClientRect()
      const speed = scrolled * intensity
      
      element.style.transform = `translateY(${speed}px)`
      
      this.animationFrameId = requestAnimationFrame(updateParallax)
    }
    
    updateParallax()
  }
  
  private stopParallax() {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId)
      this.animationFrameId = null
    }
  }
  
  /**
   * 添加磁性吸附效果
   */
  addMagneticEffect(element: HTMLElement, strength = 0.3) {
    const handleMouseMove = (event: MouseEvent) => {
      const rect = element.getBoundingClientRect()
      const centerX = rect.left + rect.width / 2
      const centerY = rect.top + rect.height / 2
      
      const deltaX = (event.clientX - centerX) * strength
      const deltaY = (event.clientY - centerY) * strength
      
      element.style.transform = `translate(${deltaX}px, ${deltaY}px)`
    }
    
    const handleMouseLeave = () => {
      element.style.transform = 'translate(0, 0)'
    }
    
    element.addEventListener('mousemove', handleMouseMove)
    element.addEventListener('mouseleave', handleMouseLeave)
    
    return () => {
      element.removeEventListener('mousemove', handleMouseMove)
      element.removeEventListener('mouseleave', handleMouseLeave)
    }
  }
  
  /**
   * 添加呼吸灯效果
   */
  addBreathingEffect(element: HTMLElement, duration = 2000) {
    element.style.animation = `breathing ${duration}ms ease-in-out infinite`
    
    // 添加CSS动画
    if (!document.querySelector('#breathing-styles')) {
      const style = document.createElement('style')
      style.id = 'breathing-styles'
      style.textContent = `
        @keyframes breathing {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.7; }
        }
      `
      document.head.appendChild(style)
    }
  }
  
  /**
   * 添加打字机效果
   */
  addTypewriterEffect(element: HTMLElement, text: string, speed = 100) {
    element.textContent = ''
    let index = 0
    
    const type = () => {
      if (index < text.length) {
        element.textContent += text.charAt(index)
        index++
        setTimeout(type, speed)
      }
    }
    
    type()
  }
  
  /**
   * 清理所有效果
   */
  cleanup() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId)
      this.animationFrameId = null
    }
  }
}

/**
 * 动态色彩系统
 */
export class DynamicColorSystem {
  private static currentTheme: 'light' | 'dark' | 'auto' = 'auto'
  private static timeBasedColors: Map<string, string> = new Map()
  
  /**
   * 根据时间调整色彩
   */
  static applyTimeBasedColors() {
    const hour = new Date().getHours()
    const root = document.documentElement
    
    // 早晨 (6-10点) - 清新色调
    if (hour >= 6 && hour < 10) {
      root.style.setProperty('--dynamic-accent', '#00D084')
      root.style.setProperty('--dynamic-background', '#F0FFF4')
      root.style.setProperty('--dynamic-surface', '#FFFFFF')
    }
    // 下午 (14-18点) - 活力色调
    else if (hour >= 14 && hour < 18) {
      root.style.setProperty('--dynamic-accent', '#07C160')
      root.style.setProperty('--dynamic-background', '#EDEDED')
      root.style.setProperty('--dynamic-surface', '#FFFFFF')
    }
    // 晚上 (19-23点) - 温暖色调
    else if (hour >= 19 && hour < 23) {
      root.style.setProperty('--dynamic-accent', '#06AD56')
      root.style.setProperty('--dynamic-background', '#F5F5F5')
      root.style.setProperty('--dynamic-surface', '#FAFAFA')
    }
    // 深夜/凌晨 - 柔和色调
    else {
      root.style.setProperty('--dynamic-accent', '#4CAF50')
      root.style.setProperty('--dynamic-background', '#F8F8F8')
      root.style.setProperty('--dynamic-surface', '#FFFFFF')
    }
  }
  
  /**
   * 根据环境光调整色彩
   */
  static adjustForAmbientLight() {
    // 检测用户的颜色方案偏好
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches
    
    const root = document.documentElement
    
    if (prefersDark) {
      root.style.setProperty('--dynamic-background', '#1A1A1A')
      root.style.setProperty('--dynamic-surface', '#2A2A2A')
      root.style.setProperty('--dynamic-text', '#FFFFFF')
    }
    
    if (prefersHighContrast) {
      root.style.setProperty('--dynamic-border', '#000000')
      root.style.setProperty('--dynamic-text', '#000000')
    }
  }
  
  /**
   * 初始化动态色彩系统
   */
  static init() {
    this.applyTimeBasedColors()
    this.adjustForAmbientLight()
    
    // 每小时更新一次时间色彩
    setInterval(() => {
      this.applyTimeBasedColors()
    }, 60 * 60 * 1000)
    
    // 监听系统主题变化
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
      this.adjustForAmbientLight()
    })
  }
}
