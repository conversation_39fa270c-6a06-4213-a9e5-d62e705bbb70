@echo off
echo 检查后端服务器状态...
echo.

echo 1. 检查端口5057是否被占用
netstat -an | findstr :5057
if %errorlevel% == 0 (
    echo ✅ 端口5057正在使用中
) else (
    echo ❌ 端口5057未被占用，后端服务器可能未启动
    echo.
    echo 请在后端项目目录运行以下命令启动后端：
    echo dotnet run
    echo.
)

echo.
echo 2. 尝试访问后端API
curl -s -o nul -w "HTTP状态码: %%{http_code}" http://localhost:5057/api/health
if %errorlevel% == 0 (
    echo.
    echo ✅ 后端API可访问
) else (
    echo.
    echo ❌ 无法访问后端API
)

echo.
echo 3. 检查防火墙设置
echo 请确保Windows防火墙允许端口5057和5173的通信

pause
