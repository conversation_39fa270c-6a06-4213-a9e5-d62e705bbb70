@echo off
echo 检查后端服务器状态...
echo.

echo 1. 检查HTTP端口5057
netstat -an | findstr :5057
if %errorlevel% == 0 (
    echo ✅ HTTP端口5057正在使用中
) else (
    echo ❌ HTTP端口5057未被占用
)

echo.
echo 2. 检查HTTPS端口7250
netstat -an | findstr :7250
if %errorlevel% == 0 (
    echo ✅ HTTPS端口7250正在使用中
) else (
    echo ❌ HTTPS端口7250未被占用，后端服务器可能未启动
    echo.
    echo 请运行以下命令启动后端：
    echo start-backend.bat
    echo 或者手动运行: dotnet run --launch-profile https
    echo.
)

echo.
echo 3. 尝试访问后端API (HTTPS)
curl -k -s -o nul -w "HTTPS API状态码: %%{http_code}" https://localhost:7250/api/auth/test 2>nul
if %errorlevel% == 0 (
    echo.
    echo ✅ 后端HTTPS API可访问
) else (
    echo.
    echo ❌ 无法访问后端HTTPS API
)

echo.
echo 4. 检查防火墙设置
echo 请确保Windows防火墙允许端口7250、5057和5173的通信

pause
