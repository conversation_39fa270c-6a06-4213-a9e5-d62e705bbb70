<template>
  <div class="chat-test-container">
    <van-nav-bar
      title="聊天功能测试"
      left-text="返回"
      left-arrow
      @click-left="goBack"
      fixed
      placeholder
    />

    <div class="test-content">
      <div class="test-section">
        <h3>聊天页面测试</h3>
        <div class="test-buttons">
          <van-button 
            type="primary" 
            block 
            @click="openOptimizedChat"
            class="test-button"
          >
            🚀 优化版聊天页面
          </van-button>
          
          <van-button 
            type="default" 
            block 
            @click="openOriginalChat"
            class="test-button"
          >
            📱 原版聊天页面
          </van-button>
          
          <van-button 
            type="warning" 
            block 
            @click="openScrollTest"
            class="test-button"
          >
            📜 滚动功能测试
          </van-button>
        </div>
      </div>

      <div class="test-section">
        <h3>功能特性对比</h3>
        <div class="feature-comparison">
          <div class="feature-item">
            <div class="feature-title">✅ 智能消息滚动</div>
            <div class="feature-desc">发送消息自动滚动，接收消息智能判断</div>
          </div>
          
          <div class="feature-item">
            <div class="feature-title">⌨️ 键盘适配</div>
            <div class="feature-desc">键盘弹出/收起时动态调整消息列表</div>
          </div>
          
          <div class="feature-item">
            <div class="feature-title">📝 长消息处理</div>
            <div class="feature-desc">支持展开/折叠，避免占据过多空间</div>
          </div>
          
          <div class="feature-item">
            <div class="feature-title">🎯 精确定位</div>
            <div class="feature-desc">消息完全可见，不被输入框遮挡</div>
          </div>
          
          <div class="feature-item">
            <div class="feature-title">🔄 性能优化</div>
            <div class="feature-desc">流畅滚动，减少卡顿和重绘</div>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>测试场景</h3>
        <div class="test-scenarios">
          <div class="scenario-item">
            <div class="scenario-title">📤 发送消息测试</div>
            <div class="scenario-steps">
              <div>1. 发送短消息，观察滚动行为</div>
              <div>2. 发送长消息，测试展开折叠</div>
              <div>3. 连续发送多条消息</div>
            </div>
          </div>
          
          <div class="scenario-item">
            <div class="scenario-title">📥 接收消息测试</div>
            <div class="scenario-steps">
              <div>1. 在底部时接收消息（自动滚动）</div>
              <div>2. 查看历史时接收消息（不滚动）</div>
              <div>3. 显示未读消息提示</div>
            </div>
          </div>
          
          <div class="scenario-item">
            <div class="scenario-title">⌨️ 键盘交互测试</div>
            <div class="scenario-steps">
              <div>1. 点击输入框弹出键盘</div>
              <div>2. 观察消息列表高度调整</div>
              <div>3. 键盘收起后恢复正常</div>
            </div>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>调试工具</h3>
        <div class="debug-tools">
          <van-button 
            size="small" 
            @click="runScrollTest"
            :loading="testRunning"
          >
            🧪 运行滚动测试
          </van-button>
          
          <van-button 
            size="small" 
            @click="getScrollStatus"
          >
            📊 获取滚动状态
          </van-button>
          
          <van-button 
            size="small" 
            @click="clearConsole"
          >
            🧹 清空控制台
          </van-button>
        </div>
        
        <div class="debug-output" v-if="debugOutput">
          <pre>{{ debugOutput }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()
const testRunning = ref(false)
const debugOutput = ref('')

// 返回上一页
const goBack = () => {
  router.back()
}

// 打开优化版聊天页面
const openOptimizedChat = () => {
  router.push('/optimized-chat/test?type=private')
}

// 打开原版聊天页面
const openOriginalChat = () => {
  router.push('/chat/test?type=private')
}

// 打开滚动测试页面
const openScrollTest = () => {
  router.push('/scroll-test')
}

// 运行滚动测试
const runScrollTest = async () => {
  testRunning.value = true
  debugOutput.value = '正在运行滚动测试...\n'
  
  try {
    // 检查是否有测试工具
    if (typeof (window as any).testChatScroll === 'function') {
      const result = await (window as any).testChatScroll()
      debugOutput.value += `测试结果: ${result ? '✅ 通过' : '❌ 失败'}\n`
      debugOutput.value += '详细结果请查看控制台\n'
    } else {
      debugOutput.value += '❌ 测试工具未加载，请先打开聊天页面\n'
    }
  } catch (error) {
    debugOutput.value += `❌ 测试执行失败: ${error}\n`
  } finally {
    testRunning.value = false
  }
}

// 获取滚动状态
const getScrollStatus = () => {
  try {
    if (typeof (window as any).getScrollStatus === 'function') {
      const status = (window as any).getScrollStatus()
      debugOutput.value = JSON.stringify(status, null, 2)
    } else {
      debugOutput.value = '❌ 状态工具未加载，请先打开聊天页面'
    }
  } catch (error) {
    debugOutput.value = `❌ 获取状态失败: ${error}`
  }
}

// 清空控制台
const clearConsole = () => {
  console.clear()
  debugOutput.value = ''
  showToast('控制台已清空')
}
</script>

<style scoped>
.chat-test-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.test-content {
  padding: 16px;
  padding-bottom: 32px;
}

.test-section {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.test-button {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

.feature-comparison {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-item {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #07C160;
}

.feature-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.feature-desc {
  font-size: 14px;
  color: #666;
}

.test-scenarios {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.scenario-item {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.scenario-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.scenario-steps {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.scenario-steps div {
  font-size: 14px;
  color: #666;
  padding-left: 8px;
}

.debug-tools {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.debug-output {
  background: #1e1e1e;
  color: #d4d4d4;
  padding: 12px;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.debug-output pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .test-content {
    padding: 12px;
  }
  
  .test-section {
    padding: 12px;
  }
  
  .debug-tools {
    justify-content: center;
  }
}
</style>
