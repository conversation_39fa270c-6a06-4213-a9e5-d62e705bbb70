<template>
  <div class="debug-page">
    <van-nav-bar title="调试信息" left-arrow @click-left="$router.back()" />
    
    <div class="debug-content">
      <!-- 认证状态 -->
      <van-cell-group title="认证状态">
        <van-cell title="登录状态" :value="authStore.isAuthenticated ? '已登录' : '未登录'" />
        <van-cell title="用户信息" :value="authStore.user?.username || '无'" />
        <van-cell title="Token状态" :value="tokenStatus" />
      </van-cell-group>

      <!-- SignalR状态 -->
      <van-cell-group title="SignalR连接状态">
        <van-cell title="连接状态" :value="signalRStatus.state" />
        <van-cell title="连接消息" :value="signalRStatus.message" />
        <van-cell title="是否连接" :value="signalRStatus.isConnected ? '是' : '否'" />
      </van-cell-group>

      <!-- 操作按钮 -->
      <div class="debug-actions">
        <van-button type="primary" block @click="refreshStatus">刷新状态</van-button>
        <van-button type="warning" block @click="testSignalR" :loading="testing">测试SignalR连接</van-button>
        <van-button type="danger" block @click="clearStorage">清除本地存储</van-button>
      </div>

      <!-- 日志信息 -->
      <van-cell-group title="调试日志">
        <div class="debug-logs">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </van-cell-group>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import signalRService from '@/services/signalr'

const authStore = useAuthStore()
const testing = ref(false)
const logs = ref<Array<{ time: string, message: string }>>([])

const tokenStatus = computed(() => {
  if (!authStore.token) return '无Token'
  const tokenLength = authStore.token.length
  return `有效 (${tokenLength}字符)`
})

const signalRStatus = ref({
  state: 'Unknown',
  isConnected: false,
  message: '未知'
})

const addLog = (message: string) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message
  })
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20)
  }
}

const refreshStatus = () => {
  signalRStatus.value = signalRService.getDetailedConnectionState()
  addLog('状态已刷新')
}

const testSignalR = async () => {
  testing.value = true
  addLog('开始测试SignalR连接...')
  
  try {
    if (authStore.token) {
      await signalRService.initialize(authStore.token)
      addLog('SignalR连接测试成功')
    } else {
      addLog('错误: 没有有效的认证Token')
    }
  } catch (error) {
    addLog(`SignalR连接测试失败: ${error}`)
  } finally {
    testing.value = false
    refreshStatus()
  }
}

const clearStorage = () => {
  localStorage.clear()
  addLog('本地存储已清除')
  authStore.logout()
}

onMounted(() => {
  refreshStatus()
  addLog('调试页面已加载')
})
</script>

<style scoped>
.debug-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.debug-content {
  padding: 16px;
}

.debug-actions {
  margin: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.debug-logs {
  max-height: 300px;
  overflow-y: auto;
  background: #fff;
  padding: 12px;
  border-radius: 8px;
}

.log-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 12px;
}

.log-time {
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.log-message {
  color: #333;
  flex: 1;
}
</style>
