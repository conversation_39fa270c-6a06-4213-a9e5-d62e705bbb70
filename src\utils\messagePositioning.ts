/**
 * 智能消息定位系统
 * 处理新消息到达时的自动定位、键盘弹出适配、长消息处理等
 */

export interface MessageElement {
  id: string | number
  element: HTMLElement
  timestamp: number
  isVisible: boolean
}

export interface ViewportInfo {
  height: number
  width: number
  scrollTop: number
  scrollHeight: number
  keyboardHeight: number
}

export class MessagePositioningManager {
  private container: HTMLElement | null = null
  private messages: Map<string | number, MessageElement> = new Map()
  private observers: IntersectionObserver[] = []
  private resizeObserver: ResizeObserver | null = null
  private keyboardHeight = 0
  private lastScrollPosition = 0
  private isKeyboardOpen = false
  
  constructor(container: HTMLElement) {
    this.container = container
    this.init()
  }
  
  private init() {
    if (!this.container) return
    
    // 初始化交叉观察器，监控消息可见性
    this.setupIntersectionObserver()
    
    // 初始化尺寸观察器，监控容器大小变化
    this.setupResizeObserver()
    
    // 监听键盘事件
    this.setupKeyboardListeners()
  }
  
  private setupIntersectionObserver() {
    if (!this.container) return
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          const messageId = entry.target.getAttribute('data-message-id')
          if (messageId) {
            const message = this.messages.get(messageId)
            if (message) {
              message.isVisible = entry.isIntersecting
            }
          }
        })
      },
      {
        root: this.container,
        rootMargin: '0px',
        threshold: 0.1
      }
    )
    
    this.observers.push(observer)
  }
  
  private setupResizeObserver() {
    if (!this.container) return
    
    this.resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        this.handleContainerResize(entry.contentRect)
      }
    })
    
    this.resizeObserver.observe(this.container)
  }
  
  private setupKeyboardListeners() {
    // 使用 Visual Viewport API 检测键盘
    if ('visualViewport' in window && window.visualViewport) {
      window.visualViewport.addEventListener('resize', () => {
        this.handleKeyboardChange()
      })
    }
    
    // 备用方案：监听窗口大小变化
    window.addEventListener('resize', () => {
      this.handleKeyboardChange()
    })
  }
  
  private handleContainerResize(rect: DOMRectReadOnly) {
    // 容器大小变化时重新计算消息位置
    this.recalculateMessagePositions()
  }
  
  private handleKeyboardChange() {
    const currentHeight = window.innerHeight
    const visualHeight = window.visualViewport?.height || currentHeight
    const newKeyboardHeight = currentHeight - visualHeight
    
    if (Math.abs(newKeyboardHeight - this.keyboardHeight) > 50) {
      this.keyboardHeight = newKeyboardHeight
      this.isKeyboardOpen = newKeyboardHeight > 150
      
      if (this.isKeyboardOpen) {
        this.handleKeyboardShow()
      } else {
        this.handleKeyboardHide()
      }
    }
  }
  
  private handleKeyboardShow() {
    if (!this.container) return
    
    // 保存当前滚动位置
    this.lastScrollPosition = this.container.scrollTop
    
    // 延迟调整，等待键盘动画完成
    setTimeout(() => {
      this.adjustScrollForKeyboard()
    }, 300)
  }
  
  private handleKeyboardHide() {
    if (!this.container) return
    
    // 恢复滚动位置或滚动到底部
    setTimeout(() => {
      this.scrollToBottom(true)
    }, 100)
  }
  
  private adjustScrollForKeyboard() {
    if (!this.container) return
    
    const containerHeight = this.container.clientHeight
    const scrollHeight = this.container.scrollHeight
    const currentScroll = this.container.scrollTop
    
    // 计算需要调整的滚动距离
    const adjustmentRatio = this.keyboardHeight / window.innerHeight
    const scrollAdjustment = containerHeight * adjustmentRatio
    
    // 平滑滚动到调整后的位置
    this.container.scrollTo({
      top: Math.min(currentScroll + scrollAdjustment, scrollHeight - containerHeight),
      behavior: 'smooth'
    })
  }
  
  /**
   * 注册消息元素
   */
  registerMessage(id: string | number, element: HTMLElement) {
    const messageElement: MessageElement = {
      id,
      element,
      timestamp: Date.now(),
      isVisible: false
    }
    
    this.messages.set(id, messageElement)
    element.setAttribute('data-message-id', String(id))
    
    // 开始观察这个元素
    this.observers.forEach(observer => {
      observer.observe(element)
    })
  }
  
  /**
   * 取消注册消息元素
   */
  unregisterMessage(id: string | number) {
    const message = this.messages.get(id)
    if (message) {
      this.observers.forEach(observer => {
        observer.unobserve(message.element)
      })
      this.messages.delete(id)
    }
  }
  
  /**
   * 新消息到达时的智能定位
   */
  handleNewMessage(messageId: string | number, shouldAutoScroll = true) {
    if (!shouldAutoScroll) return
    
    const isNearBottom = this.isScrollNearBottom()
    const hasUnreadMessages = this.hasUnreadMessagesInView()
    
    if (isNearBottom || !hasUnreadMessages) {
      // 如果用户在底部附近或没有未读消息在视野中，自动滚动到新消息
      setTimeout(() => {
        this.scrollToMessage(messageId)
      }, 100)
    }
  }
  
  /**
   * 滚动到指定消息
   */
  scrollToMessage(messageId: string | number, behavior: ScrollBehavior = 'smooth') {
    const message = this.messages.get(messageId)
    if (message && this.container) {
      message.element.scrollIntoView({
        behavior,
        block: 'end',
        inline: 'nearest'
      })
    }
  }
  
  /**
   * 滚动到底部
   */
  scrollToBottom(smooth = false) {
    if (!this.container) return
    
    this.container.scrollTo({
      top: this.container.scrollHeight,
      behavior: smooth ? 'smooth' : 'auto'
    })
  }
  
  /**
   * 检查是否接近底部
   */
  private isScrollNearBottom(threshold = 100): boolean {
    if (!this.container) return false
    
    const { scrollTop, scrollHeight, clientHeight } = this.container
    return scrollHeight - scrollTop - clientHeight < threshold
  }
  
  /**
   * 检查视野中是否有未读消息
   */
  private hasUnreadMessagesInView(): boolean {
    // 这里可以根据实际的未读消息逻辑来实现
    return false
  }
  
  /**
   * 重新计算所有消息位置
   */
  private recalculateMessagePositions() {
    // 重新计算消息位置，处理屏幕旋转等情况
    if (this.isKeyboardOpen) {
      this.adjustScrollForKeyboard()
    }
  }
  
  /**
   * 获取当前视口信息
   */
  getViewportInfo(): ViewportInfo {
    if (!this.container) {
      return {
        height: 0,
        width: 0,
        scrollTop: 0,
        scrollHeight: 0,
        keyboardHeight: this.keyboardHeight
      }
    }
    
    return {
      height: this.container.clientHeight,
      width: this.container.clientWidth,
      scrollTop: this.container.scrollTop,
      scrollHeight: this.container.scrollHeight,
      keyboardHeight: this.keyboardHeight
    }
  }
  
  /**
   * 清理资源
   */
  destroy() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
      this.resizeObserver = null
    }
    
    this.messages.clear()
    this.container = null
  }
}
