@echo off
echo 启动后端服务器...
echo.

cd /d "%~dp0\..\backend"

echo 当前目录: %CD%
echo.

echo 检查.NET环境...
dotnet --version
if %errorlevel% neq 0 (
    echo ❌ .NET SDK未安装或未配置
    echo 请安装.NET 8 SDK: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo.
echo 恢复NuGet包...
dotnet restore

echo.
echo 构建项目...
dotnet build

echo.
echo 启动后端服务器...
echo 🚀 后端将在以下地址启动:
echo    HTTP:  http://localhost:5057
echo    HTTPS: https://localhost:7250
echo.
echo SignalR Hub地址: /chatHub
echo.
echo 按 Ctrl+C 停止服务器
echo.

dotnet run --launch-profile https

pause
