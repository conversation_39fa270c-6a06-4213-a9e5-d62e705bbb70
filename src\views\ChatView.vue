<template>
  <div class="chat-container">
    <van-nav-bar
      :title="currentChat?.name || '聊天'"
      left-text="返回"
      left-arrow
      @click-left="goBack"
      fixed
      placeholder
    >
      <template #right>
        <van-icon name="ellipsis" size="18" @click="showChatMenu = true" />
      </template>
    </van-nav-bar>

    <!-- 消息列表 -->
    <div class="message-list" ref="messageListRef">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <div class="messages">
          <!-- 空状态 -->
          <div v-if="!currentMessages || currentMessages.length === 0" class="empty-messages">
            <div class="empty-icon">💬</div>
            <div class="empty-text">暂无聊天记录</div>
            <div class="empty-hint">发送一条消息开始聊天吧</div>
          </div>

          <!-- 按日期分组显示消息 -->
          <template v-for="(group, date) in groupedMessages" :key="date">
            <!-- 日期分隔线 -->
            <div class="date-divider wechat-date-divider">
              <span class="date-text wechat-date-text">{{ formatDate(date) }}</span>
            </div>

            <!-- 该日期的消息 -->
            <div
              v-for="(message, index) in group"
              :key="message.id"
              :class="[
                'message-item',
                { 'own-message': message.senderId === currentUserId }
              ]"
            >
              <!-- 他人消息头像 - 每条消息都显示 -->
              <div class="message-avatar">
                <van-image
                  v-if="message.senderId !== currentUserId"
                  :src="getMessageAvatar(message)"
                  class="wechat-avatar"
                  round
                  width="40"
                  height="40"
                  fit="cover"
                  :error-icon="null"
                  @error="handleAvatarError(message)"
                />
              </div>

              <div class="message-content">
                <!-- 群聊中显示发送者名称 -->
                <div
                  v-if="message.senderId !== currentUserId && chatType === 'group'"
                  class="message-sender"
                >
                  {{ message.senderName }}
                </div>

                <div
                  :class="[
                    'message-bubble',
                    'wechat-message-bubble',
                    message.senderId === currentUserId ? 'wechat-message-bubble--self' : 'wechat-message-bubble--other'
                  ]"
                  @contextmenu.prevent="onMessageContextMenu(message, $event)"
                  @long-press="onMessageLongPress(message)"
                >
                  <!-- 引用回复消息 -->
                  <QuoteReply
                    v-if="message.type === 'quote' && message.quotedMessage"
                    :quoted-message="message.quotedMessage"
                    :reply-content="message.content"
                    :is-own-message="message.senderId === currentUserId"
                    @expand="onMessageExpand(message, $event)"
                    @scroll-to-original="scrollToMessage"
                  />

                  <!-- 普通消息 -->
                  <LongMessage
                    v-else
                    :content="message.content"
                    :max-lines="5"
                    :max-length="200"
                    @expand="onMessageExpand(message, $event)"
                  />
                </div>

                <!-- 消息状态和时间 -->
                <MessageStatus
                  v-if="shouldShowTime(group, index)"
                  :status="message.status || 'sent'"
                  :timestamp="message.createdAt"
                  :is-own-message="message.senderId === currentUserId"
                  :show-time="true"
                  :read-at="message.readAt"
                  @retry="retryMessage(message)"
                />
              </div>

              <!-- 自己消息头像 - 每条消息都显示 -->
              <div class="message-avatar">
                <van-image
                  v-if="message.senderId === currentUserId"
                  :src="currentUserAvatar"
                  class="wechat-avatar"
                  round
                  width="40"
                  height="40"
                  fit="cover"
                  :error-icon="null"
                  @error="handleCurrentUserAvatarError"
                />
              </div>
            </div>
          </template>
        </div>
      </van-pull-refresh>
    </div>

    <!-- 输入框 -->
    <div class="input-area">
      <!-- 引用回复预览 -->
      <div v-if="quotingMessage" class="quote-preview">
        <div class="quote-preview-content">
          <div class="quote-preview-header">
            <span class="quote-preview-title">回复 {{ quotingMessage.senderName }}</span>
            <van-icon name="cross" size="16" @click="cancelQuote" class="quote-cancel" />
          </div>
          <div class="quote-preview-text">{{ formatQuotePreview(quotingMessage.content) }}</div>
        </div>
      </div>

      <div class="input-toolbar">
        <div class="input-actions">
          <van-button
            icon="smile-o"
            size="small"
            plain
            @click="toggleEmojiPicker"
            class="emoji-button wechat-tool-button"
            :class="{ active: showEmojiPicker }"
          />
          <van-button
            icon="plus"
            size="small"
            plain
            @click="showMoreActions"
            class="more-button wechat-tool-button"
          />
        </div>

        <van-field
          v-model="inputMessage"
          placeholder="输入消息..."
          type="textarea"
          autosize
          maxlength="1000"
          @keyup.enter="sendMessage"
          @focus="onInputFocus"
          @blur="onInputBlur"
          class="message-input wechat-input"
          ref="messageInputRef"
        />

        <van-button
          size="small"
          type="primary"
          :disabled="!inputMessage.trim()"
          @click="sendMessage"
          :loading="sending"
          class="send-button wechat-send-button"
        >
          发送
        </van-button>
      </div>

      <!-- Emoji选择器 -->
      <EmojiPicker
        :visible="showEmojiPicker"
        @select="insertEmoji"
        @close="hideEmojiPicker"
      />
    </div>

    <!-- 聊天菜单 -->
    <van-action-sheet
      v-model:show="showChatMenu"
      :actions="chatMenuActions"
      @select="onChatMenuSelect"
      cancel-text="取消"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useChatStore } from '@/stores/chat'
import { useAuthStore } from '@/stores/auth'
import { useThemeStore } from '@/stores/theme'
import EmojiPicker from '@/components/EmojiPicker.vue'
import LongMessage from '@/components/LongMessage.vue'
import QuoteReply from '@/components/QuoteReply.vue'
import MessageStatus from '@/components/MessageStatus.vue'
import { MessagePositioningManager } from '@/utils/messagePositioning'
import { MessageStatusManager, TimestampManager, ContinuousMessageManager, QuoteReplyManager } from '@/utils/messageStatus'
import type { MessageWithStatus } from '@/utils/messageStatus'
import { UIEffectsManager, DynamicColorSystem } from '@/utils/uiEffects'

const route = useRoute()
const router = useRouter()
const chatStore = useChatStore()
const authStore = useAuthStore()
const themeStore = useThemeStore()

const messageListRef = ref<HTMLElement>()
const messageInputRef = ref<HTMLElement>()
const inputMessage = ref('')
const refreshing = ref(false)
const sending = ref(false)
const showChatMenu = ref(false)
const showEmojiPicker = ref(false)
const keyboardHeight = ref(0)
const isKeyboardOpen = ref(false)
const lastScrollTop = ref(0)
let themeInterval: NodeJS.Timeout | null = null
let positioningManager: MessagePositioningManager | null = null
let statusManager: MessageStatusManager | null = null
let uiEffectsManager: UIEffectsManager | null = null
const quotingMessage = ref<MessageWithStatus | null>(null)
const showQuoteInput = ref(false)

const chatId = computed(() => route.params.id as string)
const chatType = computed(() => route.query.type as string || 'private')
const currentChat = computed(() => chatStore.currentChat)
const currentMessages = computed(() => chatStore.currentMessages)
const currentUserId = computed(() => authStore.user?.id)
// 当前用户头像 - 支持默认头像
const currentUserAvatar = computed(() => {
  const user = authStore.user
  if (!user) return getDefaultAvatar(0, '我')

  // 优先使用用户设置的头像
  if (user.avatar) {
    return user.avatar
  }

  // 使用默认头像系统
  return getDefaultAvatar(user.id, user.name || user.username || '我')
})

// 按日期分组消息
const groupedMessages = computed(() => {
  if (!currentMessages.value || currentMessages.value.length === 0) {
    return {}
  }

  const groups: { [key: string]: any[] } = {}

  currentMessages.value.forEach(message => {
    const date = new Date(message.createdAt)
    const dateKey = date.toDateString()

    if (!groups[dateKey]) {
      groups[dateKey] = []
    }
    groups[dateKey].push(message)
  })

  return groups
})

const chatMenuActions = [
  { name: '清空聊天记录', icon: 'delete-o' },
  { name: '聊天信息', icon: 'info-o' }
]

// 默认头像系统
const getDefaultAvatar = (userId: number, userName?: string) => {
  // 根据用户ID生成不同颜色的默认头像
  const colors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ]

  const colorIndex = userId % colors.length
  const bgColor = colors[colorIndex]

  // 使用用户名首字符或用户ID作为头像文字
  const avatarText = userName ? userName.charAt(0).toUpperCase() : userId.toString()

  // 生成SVG头像
  const svg = `
    <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
      <rect width="40" height="40" rx="6" fill="${bgColor}"/>
      <text x="20" y="26" text-anchor="middle" fill="white" font-size="16" font-family="PingFang SC, sans-serif" font-weight="500">
        ${avatarText}
      </text>
    </svg>
  `

  return `data:image/svg+xml;base64,${btoa(svg)}`
}

// 获取消息发送者头像
const getMessageAvatar = (message: any) => {
  // 优先使用用户真实头像
  if (message.sender?.avatar) {
    return message.sender.avatar
  }

  // 如果消息中有头像信息
  if (message.avatar) {
    return message.avatar
  }

  // 使用默认头像系统
  return getDefaultAvatar(message.senderId, message.senderName)
}

// 头像加载失败处理
const handleAvatarError = (message: any) => {
  try {
    console.warn('头像加载失败，使用默认头像:', message.senderId)
    // 这里可以更新消息的头像为默认头像
    // 实际项目中可能需要更新store中的数据
  } catch (error) {
    console.error('处理头像错误时发生异常:', error)
  }
}

const handleCurrentUserAvatarError = () => {
  try {
    console.warn('当前用户头像加载失败，使用默认头像')
    // 这里可以更新用户的头像为默认头像
  } catch (error) {
    console.error('处理当前用户头像错误时发生异常:', error)
  }
}

// 格式化日期显示
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  if (date.toDateString() === today.toDateString()) {
    return '今天'
  } else if (date.toDateString() === yesterday.toDateString()) {
    return '昨天'
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: 'long',
      day: 'numeric'
    })
  }
}

// 格式化消息时间
const formatMessageTime = (timeStr: string) => {
  const time = new Date(timeStr)
  return time.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  })
}

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim()) return

  const content = inputMessage.value.trim()
  inputMessage.value = ''
  sending.value = true

  try {
    let messageToSend: any

    // 如果是引用回复
    if (quotingMessage.value) {
      messageToSend = QuoteReplyManager.createQuoteMessage(
        quotingMessage.value,
        content,
        currentUserId.value!,
        authStore.user?.name || '我'
      )
      cancelQuote() // 清除引用状态
    } else {
      // 普通消息
      messageToSend = {
        content,
        type: 'text',
        status: 'sending'
      }
    }

    if (currentChat.value?.type === 'private') {
      const receiverId = parseInt(chatId.value)
      await chatStore.sendPrivateMessage(receiverId, messageToSend.content)
    } else if (currentChat.value?.type === 'group') {
      const groupId = parseInt(chatId.value)
      await chatStore.sendGroupMessage(groupId, messageToSend.content)
    }
    
    // 使用智能定位系统处理新消息
    await nextTick()
    try {
      if (positioningManager) {
        // 假设新消息的ID是最新的消息ID
        const latestMessage = currentMessages.value[currentMessages.value.length - 1]
        if (latestMessage) {
          positioningManager.handleNewMessage(latestMessage.id, true)
        }
      } else {
        forceScrollToBottom()
      }
    } catch (scrollError) {
      console.warn('滚动到底部失败:', scrollError)
      // 降级处理：直接滚动到底部
      try {
        forceScrollToBottom()
      } catch (fallbackError) {
        console.error('降级滚动也失败:', fallbackError)
      }
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    showToast({ message: '发送失败', type: 'fail' })
    inputMessage.value = content // 恢复输入内容
  } finally {
    sending.value = false
  }
}

// 精确计算滚动位置 - 根据实际内容高度
const calculatePreciseScrollPosition = () => {
  if (!messageListRef.value) return 0

  // 获取输入框实际高度
  const inputArea = document.querySelector('.input-area') as HTMLElement
  const inputAreaHeight = inputArea ? inputArea.offsetHeight : 80

  // 获取消息容器的实际高度
  const messagesContainer = messageListRef.value.querySelector('.messages') as HTMLElement
  const messagesHeight = messagesContainer ? messagesContainer.offsetHeight : 0

  // 获取可视区域高度
  const viewportHeight = messageListRef.value.clientHeight

  // 计算需要的滚动距离
  // 确保最后一条消息完全可见，且不被输入框遮挡
  const targetScrollTop = Math.max(0, messagesHeight - viewportHeight + inputAreaHeight + 20) // 20px额外边距

  return targetScrollTop
}

// 智能滚动到底部 - 精确计算
const scrollToBottom = (smooth = false) => {
  if (!messageListRef.value) return

  const targetScrollTop = calculatePreciseScrollPosition()

  const scrollOptions = {
    top: targetScrollTop,
    behavior: smooth ? 'smooth' : 'auto'
  } as ScrollToOptions

  messageListRef.value.scrollTo(scrollOptions)
}

// 强制滚动到底部（用于发送消息后）- 多阶段精确滚动
const forceScrollToBottom = () => {
  // 第一阶段：立即滚动（基于当前DOM状态）
  scrollToBottom(false)

  // 第二阶段：等待DOM更新后精确滚动
  nextTick(() => {
    setTimeout(() => {
      scrollToBottom(true)
    }, 50)
  })

  // 第三阶段：等待动画和布局完成后最终确认
  setTimeout(() => {
    const currentScrollTop = messageListRef.value?.scrollTop || 0
    const targetScrollTop = calculatePreciseScrollPosition()

    // 如果滚动位置不准确，进行最终调整
    if (Math.abs(currentScrollTop - targetScrollTop) > 10) {
      scrollToBottom(false)
    }
  }, 350)
}

// 刷新消息
const onRefresh = async () => {
  // TODO: 加载更多历史消息
  refreshing.value = false
}

// 返回
const goBack = () => {
  router.back()
}

// 聊天菜单选择
const onChatMenuSelect = (action: any) => {
  showChatMenu.value = false

  if (action.name === '清空聊天记录') {
    // TODO: 实现清空聊天记录
    console.log('清空聊天记录')
  } else if (action.name === '聊天信息') {
    // TODO: 显示聊天信息
    console.log('聊天信息')
  }
}

// Emoji相关方法
const toggleEmojiPicker = () => {
  try {
    showEmojiPicker.value = !showEmojiPicker.value

    // 如果打开表情选择器，隐藏键盘
    if (showEmojiPicker.value) {
      if (messageInputRef.value) {
        const input = messageInputRef.value.querySelector('textarea') as HTMLTextAreaElement
        if (input) {
          input.blur() // 隐藏键盘
        }
      }
    }
  } catch (error) {
    console.error('切换表情选择器失败:', error)
  }
}

const hideEmojiPicker = () => {
  try {
    showEmojiPicker.value = false
  } catch (error) {
    console.error('隐藏表情选择器失败:', error)
  }
}

// 表情插入防抖处理
let emojiInsertTimer: NodeJS.Timeout | null = null

const insertEmoji = (emoji: string) => {
  try {
    // 防止连续快速选择导致的异常
    if (emojiInsertTimer) {
      clearTimeout(emojiInsertTimer)
    }

    // 直接更新输入内容，避免不必要的DOM操作
    inputMessage.value += emoji

    // 延迟处理焦点，避免频繁操作
    emojiInsertTimer = setTimeout(() => {
      try {
        if (messageInputRef.value) {
          const input = messageInputRef.value.querySelector('textarea') as HTMLTextAreaElement
          if (input) {
            // 确保输入框存在且可操作
            if (document.contains(input)) {
              input.focus()
              // 将光标移到末尾
              const length = input.value.length
              input.setSelectionRange(length, length)
            }
          }
        }
      } catch (focusError) {
        console.warn('设置输入框焦点失败:', focusError)
        // 焦点设置失败不影响表情插入功能
      }
    }, 100) // 100ms延迟，避免频繁操作

  } catch (error) {
    console.error('插入表情失败:', error)
    // 即使出错也要确保表情能插入
    inputMessage.value += emoji
  }
}

const showMoreActions = () => {
  showToast({ message: '更多功能开发中...', type: 'loading' })
}

// 移除连续消息判断 - 每条消息都独立显示

const shouldShowTime = (messages: any[], index: number) => {
  if (index === messages.length - 1) return true // 最后一条消息总是显示时间

  const currentMessage = messages[index]
  const prevMessage = index > 0 ? messages[index - 1] : undefined

  return TimestampManager.shouldShowTimestamp(currentMessage, prevMessage)
}

// 消息交互方法
const onMessageContextMenu = (message: any, event: MouseEvent) => {
  // 右键菜单处理
  event.preventDefault()
  showMessageMenu(message, event)
}

const onMessageLongPress = (message: any) => {
  // 长按处理（移动端）
  showMessageMenu(message)
}

const showMessageMenu = (message: any, event?: MouseEvent) => {
  // 显示消息操作菜单（复制、引用、删除等）
  quotingMessage.value = message
  showQuoteInput.value = true
}

// 引用回复相关方法
const cancelQuote = () => {
  quotingMessage.value = null
  showQuoteInput.value = false
}

const formatQuotePreview = (content: string): string => {
  return QuoteReplyManager.formatQuoteContent({ content } as MessageWithStatus)
}

// 重试发送消息
const retryMessage = (message: any) => {
  // 重新发送失败的消息
  console.log('重试发送消息:', message)
}

// 应用高级UI效果
const applyAdvancedUIEffects = () => {
  if (!uiEffectsManager) return

  // 为消息气泡添加悬停效果
  const messageBubbles = document.querySelectorAll('.wechat-message-bubble')
  messageBubbles.forEach(bubble => {
    uiEffectsManager!.addHoverEffect(bubble as HTMLElement, {
      scale: 1.02,
      shadow: { elevation: 'soft', animated: true }
    })
  })

  // 为发送按钮添加波纹效果
  const sendButton = document.querySelector('.wechat-send-button')
  if (sendButton) {
    uiEffectsManager!.addRippleEffect(sendButton as HTMLElement)
  }

  // 为输入框添加毛玻璃效果
  const inputArea = document.querySelector('.input-area')
  if (inputArea) {
    uiEffectsManager!.applyGlassEffect(inputArea as HTMLElement, {
      blur: 20,
      opacity: 0.95,
      borderOpacity: 0.3,
      shadowIntensity: 0.1
    })
  }

  // 为工具按钮添加磁性效果
  const toolButtons = document.querySelectorAll('.wechat-tool-button')
  toolButtons.forEach(button => {
    uiEffectsManager!.addMagneticEffect(button as HTMLElement, 0.2)
  })
}

// 消息展开处理
const onMessageExpand = (message: any, expanded: boolean) => {
  message.expanded = expanded

  // 展开后重新注册消息到定位管理器
  if (positioningManager && expanded) {
    setTimeout(() => {
      const messageElement = document.querySelector(`[data-message-id="${message.id}"]`)
      if (messageElement) {
        positioningManager.registerMessage(message.id, messageElement as HTMLElement)
      }
    }, 100)
  }
}

// 键盘和输入框相关方法
const onInputFocus = () => {
  hideEmojiPicker()
  handleKeyboardShow()
}

const onInputBlur = () => {
  handleKeyboardHide()
}

const handleKeyboardShow = () => {
  isKeyboardOpen.value = true
  // 保存当前滚动位置
  if (messageListRef.value) {
    lastScrollTop.value = messageListRef.value.scrollTop
  }

  // 延迟调整滚动位置，等待键盘完全弹出
  setTimeout(() => {
    adjustScrollForKeyboard()
  }, 300)
}

const handleKeyboardHide = () => {
  isKeyboardOpen.value = false
  keyboardHeight.value = 0
}

const adjustScrollForKeyboard = () => {
  if (!messageListRef.value) return

  // 计算键盘高度（简化处理）
  const viewportHeight = window.innerHeight
  const documentHeight = document.documentElement.clientHeight
  keyboardHeight.value = Math.max(0, documentHeight - viewportHeight)

  // 调整消息列表滚动位置
  if (keyboardHeight.value > 0) {
    const scrollOffset = keyboardHeight.value * 0.5
    messageListRef.value.scrollTop = lastScrollTop.value + scrollOffset
  }
}

// 屏幕旋转处理
const handleOrientationChange = () => {
  setTimeout(() => {
    // 重新计算视口高度
    const vh = window.innerHeight * 0.01
    document.documentElement.style.setProperty('--vh', `${vh}px`)

    // 调整滚动位置
    if (messageListRef.value) {
      scrollToBottom(true)
    }
  }, 100)
}

// 视口高度变化处理
const handleResize = () => {
  const vh = window.innerHeight * 0.01
  document.documentElement.style.setProperty('--vh', `${vh}px`)

  // 检测键盘状态
  const currentHeight = window.innerHeight
  const standardHeight = window.screen.height

  if (currentHeight < standardHeight * 0.75) {
    handleKeyboardShow()
  } else {
    handleKeyboardHide()
  }
}

// 检查用户是否在底部附近
const isNearBottom = () => {
  if (!messageListRef.value) return true

  const { scrollTop, scrollHeight, clientHeight } = messageListRef.value
  const threshold = 150 // 150px阈值

  return scrollHeight - scrollTop - clientHeight < threshold
}

// 智能滚动：只有在用户接近底部时才自动滚动
const smartScrollToBottom = () => {
  if (isNearBottom()) {
    // 延迟滚动，等待新消息渲染完成
    nextTick(() => {
      setTimeout(() => {
        scrollToBottom(true)
      }, 100)
    })
  }
}

// 监听消息容器高度变化
const observeMessageHeightChanges = () => {
  if (!messageListRef.value) return

  const messagesContainer = messageListRef.value.querySelector('.messages')
  if (!messagesContainer) return

  // 使用ResizeObserver监听消息容器高度变化
  const resizeObserver = new ResizeObserver(() => {
    // 如果用户在底部附近，自动调整滚动位置
    if (isNearBottom()) {
      setTimeout(() => {
        scrollToBottom(true)
      }, 50)
    }
  })

  resizeObserver.observe(messagesContainer)

  // 返回observer以便清理
  return resizeObserver
}

// 监听消息变化，智能滚动
watch(currentMessages, async (newMessages, oldMessages) => {
  await nextTick()

  // 如果是新增消息（而不是初始加载）
  if (oldMessages && newMessages.length > oldMessages.length) {
    // 检查是否是自己发送的消息
    const latestMessage = newMessages[newMessages.length - 1]
    const currentUserId = authStore.user?.id

    if (latestMessage.senderId === currentUserId) {
      // 自己发送的消息，强制滚动到底部
      forceScrollToBottom()
    } else {
      // 收到他人消息，智能滚动
      smartScrollToBottom()
    }
  } else {
    // 初始加载或其他情况，滚动到底部
    scrollToBottom()
  }
}, { deep: true })

// 动态色彩系统
const applyTimeBasedTheme = () => {
  const hour = new Date().getHours()
  const body = document.body

  // 清除之前的时段类名
  body.classList.remove('time-morning', 'time-afternoon', 'time-evening')

  if (hour >= 6 && hour < 10) {
    body.classList.add('time-morning')
  } else if (hour >= 14 && hour < 18) {
    body.classList.add('time-afternoon')
  } else if (hour >= 19 && hour < 23) {
    body.classList.add('time-evening')
  }
}

onMounted(async () => {
  // 设置微信主题
  themeStore.setTheme('wechat')

  // 设置初始视口高度
  const vh = window.innerHeight * 0.01
  document.documentElement.style.setProperty('--vh', `${vh}px`)

  // 应用时段主题
  applyTimeBasedTheme()

  // 每小时更新一次时段主题
  themeInterval = setInterval(applyTimeBasedTheme, 60 * 60 * 1000)

  // 添加事件监听器
  window.addEventListener('orientationchange', handleOrientationChange)
  window.addEventListener('resize', handleResize)

  // 初始化消息定位管理器
  if (messageListRef.value) {
    positioningManager = new MessagePositioningManager(messageListRef.value)
  }

  // 初始化消息状态管理器
  statusManager = new MessageStatusManager()

  // 初始化UI效果管理器
  uiEffectsManager = UIEffectsManager.getInstance()

  // 初始化动态色彩系统
  DynamicColorSystem.init()

  // 应用高级UI效果
  nextTick(() => {
    applyAdvancedUIEffects()

    // 启用消息高度变化监听
    const heightObserver = observeMessageHeightChanges()

    // 保存observer引用以便清理
    if (heightObserver) {
      // 在组件卸载时清理
      onUnmounted(() => {
        heightObserver.disconnect()
      })
    }
  })

  // 添加键盘检测
  if ('visualViewport' in window) {
    window.visualViewport?.addEventListener('resize', () => {
      const keyboardHeight = window.innerHeight - (window.visualViewport?.height || 0)
      if (keyboardHeight > 150) {
        handleKeyboardShow()
      } else {
        handleKeyboardHide()
      }
    })
  }
  console.log('ChatView mounted')
  console.log('Route params:', route.params)
  console.log('Route query:', route.query)
  console.log('Chat ID:', chatId.value)
  console.log('Chat Type:', chatType.value)

  // 设置当前聊天
  chatStore.setCurrentChat(chatId.value)

  // 清除未读数
  chatStore.clearUnreadCount(chatId.value)

  // 加载历史消息
  if (chatType.value === 'private') {
    console.log('Loading private chat history')
    await chatStore.loadHistoryMessages(chatId.value, 'private')
    await chatStore.markAsRead(chatId.value, 'private')
  } else if (chatType.value === 'group') {
    console.log('Loading group chat history')
    await chatStore.loadHistoryMessages(chatId.value, 'group')
    await chatStore.markAsRead(chatId.value, 'group')
  }

  console.log('Current chat:', currentChat.value)
  console.log('Current messages:', currentMessages.value)

  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('orientationchange', handleOrientationChange)
  window.removeEventListener('resize', handleResize)

  if ('visualViewport' in window) {
    window.visualViewport?.removeEventListener('resize', handleResize)
  }

  // 清理定时器
  if (themeInterval) {
    clearInterval(themeInterval)
  }

  // 清理消息定位管理器
  if (positioningManager) {
    positioningManager.destroy()
    positioningManager = null
  }

  // 清理UI效果管理器
  if (uiEffectsManager) {
    uiEffectsManager.cleanup()
    uiEffectsManager = null
  }

  // 清理表情相关定时器
  if (emojiInsertTimer) {
    clearTimeout(emojiInsertTimer)
    emojiInsertTimer = null
  }
})
</script>

<style scoped>
/* 微信风格聊天容器 */
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #EDEDED; /* 微信标准背景色 */
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Helvetica Neue', STHeiti, 'Microsoft Yahei', Tahoma, Simsun, sans-serif;
}

/* 微信风格消息列表 */
.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  padding-bottom: 80px;
  background: transparent;
  scroll-behavior: smooth;

  /* 微信风格滚动条 */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.message-list::-webkit-scrollbar {
  width: 3px;
}

.message-list::-webkit-scrollbar-track {
  background: transparent;
}

.message-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.message-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.messages {
  min-height: 100%;
}

/* 微信风格消息布局 - 每条消息独立显示 */
.message-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
  position: relative;
  padding: 0 12px;
}

.message-item.own-message {
  flex-direction: row-reverse;
}

/* 微信风格头像 */
.message-avatar {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  margin: 0 8px 0 0; /* 右边距，左边无边距 */
}

.own-message .message-avatar {
  margin: 0 0 0 8px; /* 自己的消息：左边距，右边无边距 */
}

.wechat-avatar {
  border-radius: 6px; /* 微信的头像圆角 */
  background: #f0f0f0;
}

/* 微信风格消息内容区域 */
.message-content {
  flex: 1;
  max-width: calc(100% - 60px); /* 减去头像和边距的宽度 */
  min-width: 0; /* 防止flex子元素溢出 */
}

.own-message .message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end; /* 自己的消息右对齐 */
}

.message-sender {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  padding: 0 12px;
}

.own-message .message-sender {
  text-align: right;
}

/* 微信风格消息气泡 - 重新设计 */
.message-bubble {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 10px 14px;
  box-shadow: 0 0.5px 2px rgba(0, 0, 0, 0.08);
  position: relative;
  max-width: 100%;
  word-wrap: break-word;
  font-size: 16px;
  line-height: 1.35;
  border: 0.5px solid rgba(0, 0, 0, 0.08);
  margin: 2px 0;

  /* 微信字体 */
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Helvetica Neue', STHeiti, 'Microsoft Yahei', Tahoma, Simsun, sans-serif;

  /* 微信风格动画 */
  animation: messageSlideIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: bottom left;
}

/* 消息滑入动画 */
@keyframes messageSlideIn {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 微信风格的消息气泡尾巴 - 他人消息 */
.message-bubble::before {
  content: '';
  position: absolute;
  top: 10px;
  left: -4px;
  width: 0;
  height: 0;
  border: 4px solid transparent;
  border-right-color: #FFFFFF;
  z-index: 2;
}

/* 气泡尾巴的边框 */
.message-bubble::after {
  content: '';
  position: absolute;
  top: 9.5px;
  left: -5px;
  width: 0;
  height: 0;
  border: 4.5px solid transparent;
  border-right-color: rgba(0, 0, 0, 0.08);
  z-index: 1;
}

/* 自己的消息气泡 - 微信绿色 */
.own-message .message-bubble {
  background: #95EC69;
  color: #000000;
  border: none;
  box-shadow: 0 0.5px 2px rgba(149, 236, 105, 0.3);
  transform-origin: bottom right;
}

/* 自己消息的滑入动画 */
.own-message .message-bubble {
  animation: messageSlideInRight 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes messageSlideInRight {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 自己的消息气泡尾巴（右侧） */
.own-message .message-bubble::before {
  right: -4px;
  left: auto;
  border-left-color: #95EC69;
  border-right-color: transparent;
}

.own-message .message-bubble::after {
  right: -5px;
  left: auto;
  border-left-color: #95EC69;
  border-right-color: transparent;
}

.message-text {
  word-wrap: break-word;
  line-height: 1.4;
}

.message-text.long-message {
  position: relative;
}

.expand-button {
  color: var(--theme-primary);
  cursor: pointer;
  font-size: 14px;
  margin-left: 8px;
  text-decoration: underline;
}

.expand-button:hover {
  opacity: 0.8;
}

/* 移除连续消息的特殊处理 - 每条消息都显示完整信息 */

/* 发送状态 */
.sending-status {
  color: var(--theme-text-secondary);
  font-size: 10px;
  opacity: 0.7;
}

/* 键盘弹出时的适配 */
.keyboard-open .message-list {
  padding-bottom: calc(80px + var(--keyboard-height, 0px));
}

/* 输入框高度自适应 */
.message-input :deep(.van-field__control) {
  min-height: 36px;
  max-height: 120px;
  transition: height 0.2s ease;
}

/* 引用预览样式 */
.quote-preview {
  background: var(--wechat-surface);
  border-top: 1px solid var(--wechat-border);
  padding: 12px;
  animation: slideInUp 0.3s ease-out;
}

.quote-preview-content {
  background: rgba(7, 193, 96, 0.1);
  border-left: 3px solid var(--wechat-primary);
  border-radius: 6px;
  padding: 8px 12px;
  position: relative;
}

.quote-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.quote-preview-title {
  font-size: 12px;
  color: var(--wechat-primary);
  font-weight: 500;
}

.quote-cancel {
  cursor: pointer;
  color: var(--wechat-text-secondary);
  transition: color 0.2s ease;
}

.quote-cancel:hover {
  color: var(--wechat-text-primary);
}

.quote-preview-text {
  font-size: 13px;
  color: var(--wechat-text-secondary);
  line-height: 1.3;
  max-height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 日期分隔线 */
.date-divider {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 16px 0;
  position: relative;
}

.date-divider::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--theme-border);
  opacity: 0.3;
}

.date-text {
  background: var(--theme-background);
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  color: var(--theme-text-secondary);
  position: relative;
  z-index: 1;
}

/* 消息元信息 */
.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
  font-size: 11px;
  color: var(--theme-text-secondary);
}

.own-message .message-meta {
  justify-content: flex-end;
}

.message-time {
  opacity: 0.7;
}

.read-status {
  color: var(--theme-primary);
  font-size: 10px;
}

/* 空状态 */
.empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  color: var(--theme-text-primary);
  margin-bottom: 8px;
}

.empty-hint {
  font-size: 14px;
  color: var(--theme-text-secondary);
  opacity: 0.7;
}

.message-time {
  font-size: 10px;
  opacity: 0.7;
  margin-top: 4px;
  text-align: right;
}

.input-area {
  background: var(--theme-surface);
  border-top: 1px solid var(--theme-border);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.input-toolbar {
  display: flex;
  align-items: flex-end;
  padding: 12px;
  gap: 8px;
}

.input-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.emoji-button,
.more-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  border: 1px solid var(--theme-border);
}

.emoji-button.active {
  background: var(--theme-primary);
  color: white;
  border-color: var(--theme-primary);
}

.emoji-button:hover,
.more-button:hover {
  background: var(--theme-border);
}

.message-input {
  flex: 1;
  background: var(--theme-background);
  border-radius: 20px;
  min-height: 36px;
  max-height: 120px;
}

.message-input :deep(.van-field__control) {
  background: transparent;
  border-radius: 20px;
  padding: 8px 16px;
  line-height: 1.4;
}

.send-button {
  min-width: 60px;
  height: 36px;
  border-radius: 18px;
  background: var(--theme-primary);
  border: none;
  font-weight: 500;
  color: white;
}

@media (max-width: 480px) {
  .message-list {
    padding: 12px;
  }
  
  .message-content {
    max-width: 75%;
  }
}
</style>
