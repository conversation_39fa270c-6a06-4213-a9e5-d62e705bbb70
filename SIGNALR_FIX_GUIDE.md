# SignalR连接问题修复指南

## 🔍 问题分析

**原因**: 
1. 后端配置了HTTPS端口7250，但前端配置的是HTTP端口5057
2. SignalR认证需要通过query参数传递access_token
3. Vite代理配置不匹配后端的实际端口

## ✅ 已修复的问题

### 1. 前端配置修复
- ✅ 更新SignalR服务使用正确的token传递方式
- ✅ 修复Vite代理配置指向HTTPS端口7250
- ✅ 更新环境变量配置

### 2. 后端配置优化
- ✅ 在开发环境禁用HTTPS重定向
- ✅ 添加测试API端点 `/api/auth/test`
- ✅ 保持SignalR Hub认证配置

### 3. 开发工具
- ✅ 创建后端启动脚本 `start-backend.bat`
- ✅ 更新前端启动脚本 `restart-dev.bat`
- ✅ 改进后端状态检查脚本 `check-backend.bat`

## 🚀 启动步骤

### 方法1: 使用批处理脚本（推荐）

1. **启动后端**:
   ```bash
   start-backend.bat
   ```

2. **启动前端**:
   ```bash
   restart-dev.bat
   ```

### 方法2: 手动启动

1. **启动后端**:
   ```bash
   cd ../backend
   dotnet run --launch-profile https
   ```

2. **启动前端**:
   ```bash
   npm run dev
   ```

## 🔧 验证连接

### 1. 检查后端状态
- 访问: https://localhost:7250/api/auth/test
- 应该返回: `{"message": "后端API正常运行", ...}`

### 2. 检查SignalR连接
- 登录应用后查看浏览器控制台
- 应该看到: `SignalR连接成功`

### 3. 使用调试页面
- 访问: http://localhost:5173/debug
- 查看详细的连接状态和日志

## 📋 端口配置

| 服务 | 协议 | 端口 | 用途 |
|------|------|------|------|
| 后端HTTP | HTTP | 5057 | 备用端口 |
| 后端HTTPS | HTTPS | 7250 | 主要API和SignalR |
| 前端 | HTTP | 5173 | 开发服务器 |

## 🐛 故障排除

### 如果SignalR仍然连接失败:

1. **检查后端是否启动**:
   ```bash
   check-backend.bat
   ```

2. **查看浏览器控制台错误**

3. **访问调试页面**: http://localhost:5173/debug

4. **检查防火墙设置**:
   - 确保端口7250、5057、5173未被阻止

### 常见错误及解决方案:

- **401 Unauthorized**: Token可能过期，重新登录
- **Connection refused**: 后端未启动或端口被占用
- **SSL错误**: 浏览器可能阻止自签名证书，点击"高级"→"继续访问"

## 📝 技术细节

### SignalR认证流程:
1. 用户登录获取JWT token
2. 前端通过`accessTokenFactory`传递token
3. 后端通过query参数`access_token`接收token
4. JWT中间件验证token并设置用户身份

### 代理配置:
- `/api/*` → `https://localhost:7250/api/*`
- `/chatHub` → `https://localhost:7250/chatHub`

## 🎯 预期结果

修复后您应该看到:
- ✅ 后端在HTTPS端口7250正常启动
- ✅ 前端成功连接到后端API
- ✅ SignalR连接成功建立
- ✅ 登录后可以正常使用聊天功能
- ✅ 调试页面显示所有连接状态正常
