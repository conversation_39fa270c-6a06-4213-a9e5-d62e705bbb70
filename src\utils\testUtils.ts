/**
 * 测试工具集
 * 提供单元测试、交互测试和性能测试的工具函数
 */

export interface TestResult {
  name: string
  passed: boolean
  duration: number
  error?: string
  details?: any
}

export interface PerformanceMetrics {
  renderTime: number
  scrollPerformance: number
  memoryUsage: number
  messageLoadTime: number
  interactionLatency: number
}

export class TestRunner {
  private results: TestResult[] = []
  private performanceMetrics: PerformanceMetrics = {
    renderTime: 0,
    scrollPerformance: 0,
    memoryUsage: 0,
    messageLoadTime: 0,
    interactionLatency: 0
  }
  
  /**
   * 运行单个测试
   */
  async runTest(name: string, testFn: () => Promise<void> | void): Promise<TestResult> {
    const startTime = performance.now()
    
    try {
      await testFn()
      const duration = performance.now() - startTime
      const result: TestResult = { name, passed: true, duration }
      this.results.push(result)
      return result
    } catch (error) {
      const duration = performance.now() - startTime
      const result: TestResult = { 
        name, 
        passed: false, 
        duration, 
        error: error instanceof Error ? error.message : String(error) 
      }
      this.results.push(result)
      return result
    }
  }
  
  /**
   * 运行测试套件
   */
  async runTestSuite(tests: Array<{ name: string, fn: () => Promise<void> | void }>): Promise<TestResult[]> {
    const results: TestResult[] = []
    
    for (const test of tests) {
      const result = await this.runTest(test.name, test.fn)
      results.push(result)
    }
    
    return results
  }
  
  /**
   * 获取测试结果摘要
   */
  getTestSummary(): { total: number, passed: number, failed: number, duration: number } {
    const total = this.results.length
    const passed = this.results.filter(r => r.passed).length
    const failed = total - passed
    const duration = this.results.reduce((sum, r) => sum + r.duration, 0)
    
    return { total, passed, failed, duration }
  }
  
  /**
   * 清除测试结果
   */
  clearResults(): void {
    this.results = []
  }
  
  /**
   * 获取所有测试结果
   */
  getAllResults(): TestResult[] {
    return [...this.results]
  }
  
  /**
   * 获取性能指标
   */
  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics }
  }
  
  /**
   * 更新性能指标
   */
  updatePerformanceMetrics(metrics: Partial<PerformanceMetrics>): void {
    this.performanceMetrics = { ...this.performanceMetrics, ...metrics }
  }
}

/**
 * 消息气泡渲染测试
 */
export class MessageBubbleTests {
  static async testMessageBubbleRendering(): Promise<void> {
    // 创建测试消息
    const testMessage = {
      id: 'test-1',
      content: '这是一条测试消息',
      senderId: 1,
      senderName: '测试用户',
      createdAt: new Date().toISOString(),
      type: 'text'
    }
    
    // 创建临时容器
    const container = document.createElement('div')
    container.innerHTML = `
      <div class="message-bubble wechat-message-bubble wechat-message-bubble--self">
        <div class="message-text">${testMessage.content}</div>
      </div>
    `
    
    document.body.appendChild(container)
    
    try {
      // 验证元素是否正确渲染
      const bubble = container.querySelector('.message-bubble')
      if (!bubble) throw new Error('消息气泡未正确渲染')
      
      const text = container.querySelector('.message-text')
      if (!text) throw new Error('消息文本未正确渲染')
      
      if (text.textContent !== testMessage.content) {
        throw new Error('消息内容不匹配')
      }
      
      // 验证样式是否正确应用
      const computedStyle = window.getComputedStyle(bubble)
      if (!computedStyle.borderRadius) {
        throw new Error('消息气泡样式未正确应用')
      }
      
    } finally {
      document.body.removeChild(container)
    }
  }
  
  static async testLongMessageHandling(): Promise<void> {
    const longContent = 'A'.repeat(500) // 500字符的长消息
    
    const container = document.createElement('div')
    container.innerHTML = `
      <div class="long-message-container">
        <div class="message-text collapsible">
          <div class="message-content">${longContent}</div>
        </div>
      </div>
    `
    
    document.body.appendChild(container)
    
    try {
      const messageText = container.querySelector('.message-text')
      if (!messageText) throw new Error('长消息容器未正确渲染')
      
      // 验证长消息是否被正确处理
      const computedStyle = window.getComputedStyle(messageText)
      if (computedStyle.maxHeight === 'none') {
        throw new Error('长消息未被正确限制高度')
      }
      
    } finally {
      document.body.removeChild(container)
    }
  }
  
  static async testQuoteReplyRendering(): Promise<void> {
    const quotedMessage = {
      id: 'quoted-1',
      content: '被引用的消息',
      senderName: '原发送者'
    }
    
    const container = document.createElement('div')
    container.innerHTML = `
      <div class="quote-reply-container">
        <div class="quoted-message">
          <div class="quote-line"></div>
          <div class="quote-content">
            <div class="quote-sender">${quotedMessage.senderName}</div>
            <div class="quote-text">${quotedMessage.content}</div>
          </div>
        </div>
        <div class="reply-content">回复内容</div>
      </div>
    `
    
    document.body.appendChild(container)
    
    try {
      const quoteContainer = container.querySelector('.quote-reply-container')
      if (!quoteContainer) throw new Error('引用回复容器未正确渲染')
      
      const quoteLine = container.querySelector('.quote-line')
      if (!quoteLine) throw new Error('引用线条未正确渲染')
      
      const quoteSender = container.querySelector('.quote-sender')
      if (!quoteSender || quoteSender.textContent !== quotedMessage.senderName) {
        throw new Error('引用发送者信息不正确')
      }
      
    } finally {
      document.body.removeChild(container)
    }
  }
}

/**
 * 交互测试
 */
export class InteractionTests {
  static async testMessageSending(): Promise<void> {
    // 模拟消息发送流程
    const inputElement = document.createElement('textarea')
    inputElement.value = '测试消息'
    
    const sendButton = document.createElement('button')
    sendButton.className = 'wechat-send-button'
    
    document.body.appendChild(inputElement)
    document.body.appendChild(sendButton)
    
    try {
      // 模拟用户输入
      const inputEvent = new Event('input', { bubbles: true })
      inputElement.dispatchEvent(inputEvent)
      
      // 验证发送按钮状态
      if (sendButton.disabled && inputElement.value.trim()) {
        throw new Error('发送按钮状态不正确')
      }
      
      // 模拟点击发送
      const clickEvent = new MouseEvent('click', { bubbles: true })
      sendButton.dispatchEvent(clickEvent)
      
    } finally {
      document.body.removeChild(inputElement)
      document.body.removeChild(sendButton)
    }
  }
  
  static async testKeyboardInteraction(): Promise<void> {
    const inputElement = document.createElement('textarea')
    document.body.appendChild(inputElement)
    
    try {
      // 模拟键盘事件
      const enterEvent = new KeyboardEvent('keydown', { 
        key: 'Enter', 
        bubbles: true 
      })
      
      inputElement.dispatchEvent(enterEvent)
      
      // 验证键盘事件是否正确处理
      // 这里应该检查是否触发了发送消息的逻辑
      
    } finally {
      document.body.removeChild(inputElement)
    }
  }
  
  static async testScrollPerformance(): Promise<number> {
    const container = document.createElement('div')
    container.style.height = '300px'
    container.style.overflow = 'auto'
    
    // 创建大量消息元素
    for (let i = 0; i < 1000; i++) {
      const messageDiv = document.createElement('div')
      messageDiv.className = 'message-item'
      messageDiv.innerHTML = `<div class="message-bubble">消息 ${i}</div>`
      container.appendChild(messageDiv)
    }
    
    document.body.appendChild(container)
    
    try {
      const startTime = performance.now()
      
      // 模拟滚动
      for (let i = 0; i < 10; i++) {
        container.scrollTop = i * 100
        await new Promise(resolve => requestAnimationFrame(resolve))
      }
      
      const endTime = performance.now()
      return endTime - startTime
      
    } finally {
      document.body.removeChild(container)
    }
  }
}

/**
 * 性能测试
 */
export class PerformanceTests {
  static async testMessageLoadTime(messageCount: number = 100): Promise<number> {
    const startTime = performance.now()
    
    const container = document.createElement('div')
    
    // 模拟加载大量消息
    for (let i = 0; i < messageCount; i++) {
      const messageDiv = document.createElement('div')
      messageDiv.className = 'message-item'
      messageDiv.innerHTML = `
        <div class="message-bubble wechat-message-bubble">
          <div class="message-text">这是第 ${i} 条消息，包含一些测试内容</div>
        </div>
      `
      container.appendChild(messageDiv)
    }
    
    document.body.appendChild(container)
    
    // 等待渲染完成
    await new Promise(resolve => requestAnimationFrame(resolve))
    
    const endTime = performance.now()
    
    document.body.removeChild(container)
    
    return endTime - startTime
  }
  
  static getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize / 1024 / 1024 // MB
    }
    return 0
  }
  
  static async testInteractionLatency(): Promise<number> {
    const button = document.createElement('button')
    button.style.position = 'absolute'
    button.style.top = '-1000px' // 隐藏元素
    document.body.appendChild(button)

    // 测试多次点击的平均延迟
    const measurements: number[] = []

    for (let i = 0; i < 5; i++) {
      const startTime = performance.now()

      // 模拟点击事件
      const clickEvent = new MouseEvent('click', { bubbles: true })
      button.dispatchEvent(clickEvent)

      const endTime = performance.now()
      measurements.push(endTime - startTime)

      // 短暂延迟避免连续测试
      await new Promise(resolve => setTimeout(resolve, 10))
    }

    document.body.removeChild(button)

    // 返回平均值
    return measurements.reduce((sum, val) => sum + val, 0) / measurements.length
  }
}
