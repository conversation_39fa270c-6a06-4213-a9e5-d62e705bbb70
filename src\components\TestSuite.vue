<template>
  <div class="test-suite-container">
    <div class="test-header">
      <h2>聊天应用测试套件</h2>
      <div class="test-controls">
        <van-button 
          type="primary" 
          size="small" 
          @click="runAllTests"
          :loading="isRunning"
          class="run-button"
        >
          {{ isRunning ? '测试中...' : '运行所有测试' }}
        </van-button>
        <van-button 
          size="small" 
          @click="clearResults"
          :disabled="isRunning"
        >
          清除结果
        </van-button>
      </div>
    </div>
    
    <!-- 测试结果摘要 -->
    <div v-if="testSummary" class="test-summary">
      <div class="summary-card">
        <div class="summary-item">
          <span class="label">总计:</span>
          <span class="value">{{ testSummary.total }}</span>
        </div>
        <div class="summary-item success">
          <span class="label">通过:</span>
          <span class="value">{{ testSummary.passed }}</span>
        </div>
        <div class="summary-item error">
          <span class="label">失败:</span>
          <span class="value">{{ testSummary.failed }}</span>
        </div>
        <div class="summary-item">
          <span class="label">耗时:</span>
          <span class="value">{{ Math.round(testSummary.duration) }}ms</span>
        </div>
      </div>
    </div>
    
    <!-- 性能指标 -->
    <div v-if="performanceMetrics" class="performance-metrics">
      <h3>性能指标</h3>
      <div class="metrics-grid">
        <div class="metric-item">
          <span class="metric-label">渲染时间</span>
          <span class="metric-value">{{ Math.round(performanceMetrics.renderTime) }}ms</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">滚动性能</span>
          <span class="metric-value">{{ Math.round(performanceMetrics.scrollPerformance) }}ms</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">内存使用</span>
          <span class="metric-value">{{ Math.round(performanceMetrics.memoryUsage) }}MB</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">消息加载</span>
          <span class="metric-value">{{ Math.round(performanceMetrics.messageLoadTime) }}ms</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">交互延迟</span>
          <span class="metric-value">{{ Math.round(performanceMetrics.interactionLatency) }}ms</span>
        </div>
      </div>
    </div>
    
    <!-- 详细测试结果 -->
    <div class="test-results">
      <h3>测试结果详情</h3>
      <div v-if="testResults.length === 0" class="no-results">
        暂无测试结果，点击"运行所有测试"开始测试
      </div>
      <div v-else class="results-list">
        <div 
          v-for="result in testResults" 
          :key="result.name"
          :class="['result-item', { 'passed': result.passed, 'failed': !result.passed }]"
        >
          <div class="result-header">
            <van-icon 
              :name="result.passed ? 'success' : 'close'" 
              :color="result.passed ? '#07C160' : '#F56C6C'"
              size="16"
            />
            <span class="result-name">{{ result.name }}</span>
            <span class="result-duration">{{ Math.round(result.duration) }}ms</span>
          </div>
          <div v-if="result.error" class="result-error">
            {{ result.error }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 压力测试 -->
    <div class="stress-tests">
      <h3>压力测试</h3>
      <div class="stress-controls">
        <van-field
          v-model="stressTestConfig.messageCount"
          label="消息数量"
          type="number"
          placeholder="1000"
        />
        <van-button 
          size="small" 
          @click="runStressTest"
          :loading="isStressTesting"
        >
          {{ isStressTesting ? '压力测试中...' : '运行压力测试' }}
        </van-button>
      </div>
      <div v-if="stressTestResult" class="stress-result">
        <p>加载 {{ stressTestConfig.messageCount }} 条消息耗时: {{ Math.round(stressTestResult.loadTime) }}ms</p>
        <p>滚动性能: {{ Math.round(stressTestResult.scrollTime) }}ms</p>
        <p>内存使用: {{ Math.round(stressTestResult.memoryUsage) }}MB</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import { TestRunner, MessageBubbleTests, InteractionTests, PerformanceTests } from '@/utils/testUtils'
import type { TestResult, PerformanceMetrics } from '@/utils/testUtils'

const isRunning = ref(false)
const isStressTesting = ref(false)
const testResults = ref<TestResult[]>([])
const testSummary = ref<{ total: number, passed: number, failed: number, duration: number } | null>(null)
const performanceMetrics = ref<PerformanceMetrics | null>(null)

const stressTestConfig = reactive({
  messageCount: 1000
})

const stressTestResult = ref<{
  loadTime: number
  scrollTime: number
  memoryUsage: number
} | null>(null)

const testRunner = new TestRunner()

// 运行所有测试
const runAllTests = async () => {
  if (isRunning.value) return
  
  isRunning.value = true
  testResults.value = []
  testSummary.value = null
  performanceMetrics.value = null
  
  try {
    showToast({ message: '开始运行测试...', type: 'loading' })
    
    // 定义测试套件
    const tests = [
      { name: '消息气泡渲染测试', fn: MessageBubbleTests.testMessageBubbleRendering },
      { name: '长消息处理测试', fn: MessageBubbleTests.testLongMessageHandling },
      { name: '引用回复渲染测试', fn: MessageBubbleTests.testQuoteReplyRendering },
      { name: '消息发送交互测试', fn: InteractionTests.testMessageSending },
      { name: '键盘交互测试', fn: InteractionTests.testKeyboardInteraction },
    ]
    
    // 运行测试套件
    const results = await testRunner.runTestSuite(tests)
    testResults.value = results
    testSummary.value = testRunner.getTestSummary()
    
    // 运行性能测试
    const renderTime = await PerformanceTests.testMessageLoadTime(100)
    const scrollPerformance = await InteractionTests.testScrollPerformance()
    const memoryUsage = PerformanceTests.getMemoryUsage()
    const messageLoadTime = await PerformanceTests.testMessageLoadTime(50)
    const interactionLatency = await PerformanceTests.testInteractionLatency()
    
    performanceMetrics.value = {
      renderTime,
      scrollPerformance,
      memoryUsage,
      messageLoadTime,
      interactionLatency
    }
    
    testRunner.updatePerformanceMetrics(performanceMetrics.value)
    
    const summary = testSummary.value
    if (summary.failed === 0) {
      showToast({ message: `所有测试通过! (${summary.passed}/${summary.total})`, type: 'success' })
    } else {
      showToast({ message: `测试完成: ${summary.passed}/${summary.total} 通过`, type: 'fail' })
    }
    
  } catch (error) {
    console.error('测试运行失败:', error)
    showToast({ message: '测试运行失败', type: 'fail' })
  } finally {
    isRunning.value = false
  }
}

// 清除测试结果
const clearResults = () => {
  testResults.value = []
  testSummary.value = null
  performanceMetrics.value = null
  stressTestResult.value = null
  testRunner.clearResults()
  showToast({ message: '测试结果已清除', type: 'success' })
}

// 运行压力测试
const runStressTest = async () => {
  if (isStressTesting.value) return
  
  isStressTesting.value = true
  stressTestResult.value = null
  
  try {
    showToast({ message: '开始压力测试...', type: 'loading' })
    
    const messageCount = parseInt(stressTestConfig.messageCount.toString()) || 1000
    
    // 测试消息加载时间
    const loadTime = await PerformanceTests.testMessageLoadTime(messageCount)
    
    // 测试滚动性能
    const scrollTime = await InteractionTests.testScrollPerformance()
    
    // 获取内存使用
    const memoryUsage = PerformanceTests.getMemoryUsage()
    
    stressTestResult.value = {
      loadTime,
      scrollTime,
      memoryUsage
    }
    
    showToast({ message: '压力测试完成', type: 'success' })
    
  } catch (error) {
    console.error('压力测试失败:', error)
    showToast({ message: '压力测试失败', type: 'fail' })
  } finally {
    isStressTesting.value = false
  }
}
</script>

<style scoped>
.test-suite-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  background: var(--wechat-background);
  min-height: 100vh;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--wechat-border);
}

.test-header h2 {
  margin: 0;
  color: var(--wechat-text-primary);
  font-size: 20px;
}

.test-controls {
  display: flex;
  gap: 10px;
}

.test-summary {
  margin-bottom: 20px;
}

.summary-card {
  background: var(--wechat-surface);
  border-radius: 12px;
  padding: 16px;
  box-shadow: var(--wechat-shadow-soft);
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.summary-item .label {
  font-size: 12px;
  color: var(--wechat-text-secondary);
}

.summary-item .value {
  font-size: 18px;
  font-weight: 600;
  color: var(--wechat-text-primary);
}

.summary-item.success .value {
  color: var(--wechat-primary);
}

.summary-item.error .value {
  color: #F56C6C;
}

.performance-metrics {
  margin-bottom: 20px;
}

.performance-metrics h3 {
  margin: 0 0 12px 0;
  color: var(--wechat-text-primary);
  font-size: 16px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.metric-item {
  background: var(--wechat-surface);
  border-radius: 8px;
  padding: 12px;
  box-shadow: var(--wechat-shadow-subtle);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.metric-label {
  font-size: 12px;
  color: var(--wechat-text-secondary);
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--wechat-primary);
}

.test-results {
  margin-bottom: 20px;
}

.test-results h3 {
  margin: 0 0 12px 0;
  color: var(--wechat-text-primary);
  font-size: 16px;
}

.no-results {
  text-align: center;
  color: var(--wechat-text-secondary);
  padding: 40px 20px;
  background: var(--wechat-surface);
  border-radius: 8px;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.result-item {
  background: var(--wechat-surface);
  border-radius: 8px;
  padding: 12px;
  box-shadow: var(--wechat-shadow-subtle);
}

.result-item.passed {
  border-left: 4px solid var(--wechat-primary);
}

.result-item.failed {
  border-left: 4px solid #F56C6C;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-name {
  flex: 1;
  font-weight: 500;
  color: var(--wechat-text-primary);
}

.result-duration {
  font-size: 12px;
  color: var(--wechat-text-secondary);
}

.result-error {
  margin-top: 8px;
  padding: 8px;
  background: rgba(245, 108, 108, 0.1);
  border-radius: 4px;
  font-size: 12px;
  color: #F56C6C;
}

.stress-tests h3 {
  margin: 0 0 12px 0;
  color: var(--wechat-text-primary);
  font-size: 16px;
}

.stress-controls {
  display: flex;
  gap: 12px;
  align-items: flex-end;
  margin-bottom: 12px;
}

.stress-result {
  background: var(--wechat-surface);
  border-radius: 8px;
  padding: 12px;
  box-shadow: var(--wechat-shadow-subtle);
}

.stress-result p {
  margin: 4px 0;
  font-size: 14px;
  color: var(--wechat-text-primary);
}

@media (max-width: 480px) {
  .test-suite-container {
    padding: 12px;
  }
  
  .test-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .summary-card {
    flex-direction: column;
    gap: 12px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .stress-controls {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
