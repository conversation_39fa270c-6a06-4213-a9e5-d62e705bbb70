<template>
  <div class="network-test-container">
    <van-nav-bar
      title="网络连接测试"
      left-text="返回"
      left-arrow
      @click-left="goBack"
      fixed
      placeholder
    />

    <div class="test-content">
      <div class="test-section">
        <h3>🌐 网络连接状态</h3>
        <div class="status-grid">
          <div class="status-item">
            <div class="status-label">前端服务</div>
            <div :class="['status-value', frontendStatus ? 'success' : 'error']">
              {{ frontendStatus ? '✅ 正常' : '❌ 异常' }}
            </div>
          </div>
          
          <div class="status-item">
            <div class="status-label">后端API</div>
            <div :class="['status-value', backendStatus ? 'success' : 'error']">
              {{ backendStatus ? '✅ 正常' : '❌ 异常' }}
            </div>
          </div>
          
          <div class="status-item">
            <div class="status-label">网络延迟</div>
            <div :class="['status-value', getLatencyStatus()]">
              {{ latency > 0 ? `${latency}ms` : '测试中...' }}
            </div>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>🔧 连接测试</h3>
        <div class="test-buttons">
          <van-button 
            type="primary" 
            @click="testBackendConnection"
            :loading="testing"
            block
          >
            测试后端连接
          </van-button>
          
          <van-button 
            type="default" 
            @click="testAuthAPI"
            :loading="testing"
            block
          >
            测试认证API
          </van-button>
          
          <van-button 
            type="warning" 
            @click="testSignalR"
            :loading="testing"
            block
          >
            测试SignalR连接
          </van-button>
        </div>
      </div>

      <div class="test-section">
        <h3>📊 连接信息</h3>
        <div class="info-list">
          <div class="info-item">
            <span class="info-label">前端地址:</span>
            <span class="info-value">{{ frontendUrl }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">后端地址:</span>
            <span class="info-value">{{ backendUrl }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">设备IP:</span>
            <span class="info-value">{{ deviceIP }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">用户代理:</span>
            <span class="info-value">{{ userAgent }}</span>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>📝 测试日志</h3>
        <div class="log-container">
          <div 
            v-for="(log, index) in logs" 
            :key="index"
            :class="['log-item', log.type]"
          >
            <span class="log-time">{{ formatTime(log.time) }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <van-button size="small" @click="clearLogs">清空日志</van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 状态
const frontendStatus = ref(true)
const backendStatus = ref(false)
const latency = ref(0)
const testing = ref(false)

// 连接信息
const frontendUrl = ref(window.location.origin)
const backendUrl = ref(import.meta.env.VITE_BACKEND_URL || 'https://***************:7250')
const deviceIP = ref('检测中...')
const userAgent = ref(navigator.userAgent)

// 日志
interface LogEntry {
  time: Date
  message: string
  type: 'info' | 'success' | 'error' | 'warning'
}

const logs = ref<LogEntry[]>([])

// 添加日志
const addLog = (message: string, type: LogEntry['type'] = 'info') => {
  logs.value.push({
    time: new Date(),
    message,
    type
  })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value.shift()
  }
}

// 清空日志
const clearLogs = () => {
  logs.value = []
  addLog('日志已清空', 'info')
}

// 格式化时间
const formatTime = (time: Date) => {
  return time.toLocaleTimeString()
}

// 获取延迟状态
const getLatencyStatus = () => {
  if (latency.value === 0) return 'info'
  if (latency.value < 100) return 'success'
  if (latency.value < 500) return 'warning'
  return 'error'
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 测试后端连接
const testBackendConnection = async () => {
  testing.value = true
  addLog('开始测试后端连接...', 'info')
  
  try {
    const startTime = performance.now()
    
    const response = await fetch('/api/auth/test', {
      method: 'GET',
      cache: 'no-cache'
    })
    
    const endTime = performance.now()
    latency.value = Math.round(endTime - startTime)
    
    if (response.ok) {
      const data = await response.json()
      backendStatus.value = true
      addLog(`后端连接成功: ${data.message}`, 'success')
      addLog(`响应时间: ${latency.value}ms`, 'info')
      showToast({ type: 'success', message: '后端连接正常' })
    } else {
      backendStatus.value = false
      addLog(`后端连接失败: HTTP ${response.status}`, 'error')
      showToast({ type: 'fail', message: '后端连接失败' })
    }
  } catch (error) {
    backendStatus.value = false
    addLog(`后端连接异常: ${error}`, 'error')
    showToast({ type: 'fail', message: '后端连接异常' })
  } finally {
    testing.value = false
  }
}

// 测试认证API
const testAuthAPI = async () => {
  testing.value = true
  addLog('测试认证API...', 'info')
  
  try {
    // 测试登录API（使用错误的凭据来测试API是否响应）
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'test',
        password: 'test'
      })
    })
    
    // 即使登录失败，只要API响应就说明连接正常
    if (response.status === 400 || response.status === 401) {
      addLog('认证API响应正常（预期的认证失败）', 'success')
      showToast({ type: 'success', message: '认证API正常' })
    } else if (response.ok) {
      addLog('认证API响应正常', 'success')
      showToast({ type: 'success', message: '认证API正常' })
    } else {
      addLog(`认证API异常: HTTP ${response.status}`, 'error')
      showToast({ type: 'fail', message: '认证API异常' })
    }
  } catch (error) {
    addLog(`认证API连接失败: ${error}`, 'error')
    showToast({ type: 'fail', message: '认证API连接失败' })
  } finally {
    testing.value = false
  }
}

// 测试SignalR连接
const testSignalR = async () => {
  testing.value = true
  addLog('测试SignalR连接...', 'info')
  
  try {
    // 尝试连接到SignalR Hub
    const response = await fetch('/chatHub/negotiate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      addLog('SignalR协商成功', 'success')
      showToast({ type: 'success', message: 'SignalR连接正常' })
    } else {
      addLog(`SignalR协商失败: HTTP ${response.status}`, 'error')
      showToast({ type: 'fail', message: 'SignalR连接失败' })
    }
  } catch (error) {
    addLog(`SignalR连接异常: ${error}`, 'error')
    showToast({ type: 'fail', message: 'SignalR连接异常' })
  } finally {
    testing.value = false
  }
}

// 获取设备IP
const getDeviceIP = async () => {
  try {
    // 尝试通过WebRTC获取本地IP
    const pc = new RTCPeerConnection({
      iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
    })
    
    pc.createDataChannel('')
    const offer = await pc.createOffer()
    await pc.setLocalDescription(offer)
    
    pc.onicecandidate = (event) => {
      if (event.candidate) {
        const candidate = event.candidate.candidate
        const ipMatch = candidate.match(/(\d+\.\d+\.\d+\.\d+)/)
        if (ipMatch && !ipMatch[1].startsWith('127.')) {
          deviceIP.value = ipMatch[1]
          pc.close()
        }
      }
    }
  } catch (error) {
    deviceIP.value = '无法获取'
    addLog(`获取设备IP失败: ${error}`, 'warning')
  }
}

// 初始化
onMounted(async () => {
  addLog('网络测试页面已加载', 'info')
  addLog(`前端地址: ${frontendUrl.value}`, 'info')
  addLog(`后端地址: ${backendUrl.value}`, 'info')
  
  // 获取设备IP
  await getDeviceIP()
  
  // 自动测试后端连接
  await testBackendConnection()
})
</script>

<style scoped>
.network-test-container {
  min-height: 100vh;
  background: #f5f5f5;
}

.test-content {
  padding: 16px;
  padding-bottom: 32px;
}

.test-section {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.status-item {
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.status-value {
  font-size: 14px;
  font-weight: 600;
}

.status-value.success {
  color: #07C160;
}

.status-value.error {
  color: #ee0a24;
}

.status-value.warning {
  color: #ff976a;
}

.status-value.info {
  color: #1989fa;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-label {
  font-weight: 500;
  color: #333;
}

.info-value {
  font-size: 12px;
  color: #666;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
}

.log-item {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.log-time {
  color: #999;
  white-space: nowrap;
}

.log-message {
  flex: 1;
}

.log-item.success .log-message {
  color: #07C160;
}

.log-item.error .log-message {
  color: #ee0a24;
}

.log-item.warning .log-message {
  color: #ff976a;
}

.log-item.info .log-message {
  color: #333;
}
</style>
