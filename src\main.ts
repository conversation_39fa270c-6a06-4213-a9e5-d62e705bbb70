import './assets/main.css'
import './styles/mobile.css'
import './styles/wechat.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// Vant UI组件库
import Vant from 'vant'
import 'vant/lib/index.css'

// 移动端触摸模拟器
import '@vant/touch-emulator'

// 认证store初始化
import { useAuthStore } from './stores/auth'
import { useThemeStore } from './stores/theme'

// 移动端适配
import { initMobileAdaptation } from './utils/mobile'

const app = createApp(App)

// 全局错误处理
app.config.errorHandler = (error: Error, instance, info) => {
  console.error('全局错误处理:', error)
  console.error('错误信息:', info)
  console.error('组件实例:', instance)

  // 上报错误
  try {
    console.log('错误详情:', {
      message: error.message,
      stack: error.stack,
      info,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    })
  } catch (reportError) {
    console.error('错误上报失败:', reportError)
  }
}

// 处理未捕获的Promise错误
window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise错误:', event.reason)
  event.preventDefault() // 阻止默认的错误处理
})

app.use(createPinia())
app.use(router)
app.use(Vant)

// 初始化移动端适配
initMobileAdaptation()

// 初始化认证状态
const authStore = useAuthStore()
authStore.initialize()

// 初始化主题
const themeStore = useThemeStore()
themeStore.initTheme()

// 初始化自动化测试（仅在开发环境）
if (import.meta.env.DEV) {
  import('./utils/automatedTests').then(({ AutomatedTestRunner }) => {
    const testRunner = AutomatedTestRunner.getInstance()
    testRunner.runStartupTests().then(passed => {
      if (!passed) {
        console.warn('⚠️ 启动测试未完全通过，应用可能存在问题')
      }
    })
  })

  // 加载离线消息测试工具
  import('./utils/offlineMessageTest').then(({ OfflineMessageTester }) => {
    console.log('🧪 离线消息测试工具已加载，可在控制台使用 testOfflineMessageSync() 进行测试')
  })
}

app.mount('#app')
