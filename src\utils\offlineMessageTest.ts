/**
 * 离线消息同步测试工具
 */

import { messageAPI } from '@/services/api'
import { useChatStore } from '@/stores/chat'
import { useAuthStore } from '@/stores/auth'

export class OfflineMessageTester {
  private static instance: OfflineMessageTester | null = null

  static getInstance(): OfflineMessageTester {
    if (!this.instance) {
      this.instance = new OfflineMessageTester()
    }
    return this.instance
  }

  /**
   * 测试离线消息同步功能
   */
  async testOfflineMessageSync(): Promise<boolean> {
    console.log('🧪 开始测试离线消息同步功能...')
    
    try {
      const authStore = useAuthStore()
      const chatStore = useChatStore()

      if (!authStore.isAuthenticated) {
        console.error('❌ 用户未登录，无法测试离线消息同步')
        return false
      }

      // 1. 测试获取离线消息API
      console.log('📡 测试离线消息API...')
      const testTime = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // 24小时前
      
      try {
        const response = await messageAPI.getOfflineMessages(testTime)
        console.log('✅ 离线消息API调用成功:', response.data)
        
        if (response.data) {
          const { privateMessages, groupMessages, totalCount } = response.data
          console.log(`📊 离线消息统计: 私聊 ${privateMessages?.length || 0} 条, 群聊 ${groupMessages?.length || 0} 条, 总计 ${totalCount} 条`)
        }
      } catch (apiError) {
        console.error('❌ 离线消息API调用失败:', apiError)
        return false
      }

      // 2. 测试同步功能
      console.log('🔄 测试消息同步功能...')
      try {
        await chatStore.syncOfflineMessages()
        console.log('✅ 消息同步功能正常')
      } catch (syncError) {
        console.error('❌ 消息同步失败:', syncError)
        return false
      }

      // 3. 测试本地存储
      console.log('💾 测试本地存储功能...')
      const lastOnlineTime = localStorage.getItem('lastOnlineTime')
      const lastOfflineTime = localStorage.getItem('lastOfflineTime')
      
      console.log('📅 最后在线时间:', lastOnlineTime)
      console.log('📅 最后离线时间:', lastOfflineTime)

      if (!lastOnlineTime) {
        console.warn('⚠️ 未找到最后在线时间记录')
        // 设置当前时间作为在线时间
        localStorage.setItem('lastOnlineTime', new Date().toISOString())
      }

      console.log('✅ 离线消息同步功能测试完成')
      return true

    } catch (error) {
      console.error('❌ 离线消息同步测试失败:', error)
      return false
    }
  }

  /**
   * 模拟用户离线再上线的场景
   */
  async simulateOfflineOnlineScenario(): Promise<boolean> {
    console.log('🎭 模拟用户离线再上线场景...')
    
    try {
      const authStore = useAuthStore()
      const chatStore = useChatStore()

      // 1. 记录当前时间作为离线时间
      const offlineTime = new Date().toISOString()
      localStorage.setItem('lastOfflineTime', offlineTime)
      console.log('📴 模拟用户离线，时间:', offlineTime)

      // 2. 等待一段时间（模拟离线期间）
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 3. 模拟用户重新上线
      const onlineTime = new Date().toISOString()
      localStorage.setItem('lastOnlineTime', onlineTime)
      console.log('📱 模拟用户上线，时间:', onlineTime)

      // 4. 触发离线消息同步
      await chatStore.syncOfflineMessages()
      console.log('✅ 离线消息同步完成')

      return true

    } catch (error) {
      console.error('❌ 模拟离线上线场景失败:', error)
      return false
    }
  }

  /**
   * 检查消息同步状态
   */
  checkSyncStatus(): void {
    console.log('📊 检查消息同步状态...')
    
    const chatStore = useChatStore()
    const totalSessions = chatStore.chatSessions.length
    const totalMessages = Object.keys(chatStore.messages).reduce((total, chatId) => {
      return total + (chatStore.messages[chatId]?.length || 0)
    }, 0)

    console.log(`💬 当前会话数: ${totalSessions}`)
    console.log(`📝 当前消息总数: ${totalMessages}`)
    console.log(`🔔 未读消息数: ${chatStore.totalUnreadCount}`)

    // 显示每个会话的详细信息
    chatStore.chatSessions.forEach(session => {
      const messageCount = chatStore.messages[session.id]?.length || 0
      console.log(`📋 会话 "${session.name}": ${messageCount} 条消息, ${session.unreadCount} 条未读`)
    })
  }

  /**
   * 运行完整的测试套件
   */
  async runFullTest(): Promise<boolean> {
    console.log('🚀 开始运行离线消息同步完整测试...')
    
    let allTestsPassed = true

    // 1. 基础功能测试
    const basicTest = await this.testOfflineMessageSync()
    if (!basicTest) {
      allTestsPassed = false
    }

    // 2. 场景模拟测试
    const scenarioTest = await this.simulateOfflineOnlineScenario()
    if (!scenarioTest) {
      allTestsPassed = false
    }

    // 3. 状态检查
    this.checkSyncStatus()

    if (allTestsPassed) {
      console.log('🎉 所有离线消息同步测试通过！')
    } else {
      console.log('❌ 部分测试失败，请检查相关功能')
    }

    return allTestsPassed
  }
}

// 导出便捷方法
export const testOfflineMessageSync = () => {
  return OfflineMessageTester.getInstance().runFullTest()
}

// 在开发环境下暴露到全局
if (import.meta.env.DEV) {
  (window as any).testOfflineMessageSync = testOfflineMessageSync
  (window as any).OfflineMessageTester = OfflineMessageTester
}
