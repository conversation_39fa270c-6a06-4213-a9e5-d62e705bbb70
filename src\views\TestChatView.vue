<template>
  <div class="test-chat-container">
    <van-nav-bar title="聊天测试" left-arrow @click-left="$router.back()" />
    
    <div class="test-messages">
      <div class="message-item">
        <div class="message-avatar">
          <van-image
            src="https://via.placeholder.com/32/4CAF50/FFFFFF?text=A"
            class="wechat-avatar"
            round
            width="32"
            height="32"
            fit="cover"
          />
        </div>
        <div class="message-content">
          <div class="message-bubble">
            你好！这是第一条消息
          </div>
        </div>
      </div>

      <div class="message-item continuous">
        <div class="message-avatar">
          <van-image
            src="https://via.placeholder.com/32/4CAF50/FFFFFF?text=A"
            class="wechat-avatar avatar-hidden"
            round
            width="32"
            height="32"
            fit="cover"
          />
        </div>
        <div class="message-content">
          <div class="message-bubble">
            这是连续的第二条消息
          </div>
        </div>
      </div>

      <div class="message-item continuous">
        <div class="message-avatar">
          <van-image
            src="https://via.placeholder.com/32/4CAF50/FFFFFF?text=A"
            class="wechat-avatar avatar-hidden"
            round
            width="32"
            height="32"
            fit="cover"
          />
        </div>
        <div class="message-content">
          <div class="message-bubble">
            这是连续的第三条消息
          </div>
        </div>
      </div>

      <div class="message-item own-message">
        <div class="message-content">
          <div class="message-bubble">
            我的回复消息
          </div>
        </div>
        <div class="message-avatar">
          <van-image
            src="https://via.placeholder.com/32/2196F3/FFFFFF?text=我"
            class="wechat-avatar"
            round
            width="32"
            height="32"
            fit="cover"
          />
        </div>
      </div>

      <div class="message-item own-message continuous">
        <div class="message-content">
          <div class="message-bubble">
            我的连续消息
          </div>
        </div>
        <div class="message-avatar">
          <van-image
            src="https://via.placeholder.com/32/2196F3/FFFFFF?text=我"
            class="wechat-avatar avatar-hidden"
            round
            width="32"
            height="32"
            fit="cover"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 测试页面，无需复杂逻辑
</script>

<style scoped>
.test-chat-container {
  height: 100vh;
  background: #f5f5f5;
}

.test-messages {
  padding: 16px;
}

/* 复制主要的消息样式 */
.message-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-end;
  position: relative;
}

.message-item.own-message {
  flex-direction: row-reverse;
}

.message-item:not(.continuous) {
  margin-bottom: 16px;
}

.message-avatar {
  flex-shrink: 0;
  margin: 0 8px;
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.message-bubble {
  background: #ffffff;
  border-radius: 18px;
  padding: 12px 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  word-wrap: break-word;
  font-size: 16px;
  line-height: 1.4;
  border: 1px solid #e0e0e0;
}

.message-bubble::before {
  content: '';
  position: absolute;
  top: 12px;
  left: -6px;
  width: 0;
  height: 0;
  border: 6px solid transparent;
  border-right-color: #ffffff;
}

.own-message .message-bubble {
  background: #07C160;
  color: #000;
  border: none;
}

.own-message .message-bubble::before {
  right: -6px;
  left: auto;
  border-left-color: #07C160;
  border-right-color: transparent;
}

/* 连续消息样式 */
.message-item.continuous {
  margin-bottom: 4px;
}

.message-item.continuous .message-bubble {
  margin-top: 2px;
}

.message-item.continuous .message-bubble::before {
  display: none;
}

.message-item.continuous .message-avatar {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.wechat-avatar.avatar-hidden {
  opacity: 0;
  visibility: hidden;
}
</style>
