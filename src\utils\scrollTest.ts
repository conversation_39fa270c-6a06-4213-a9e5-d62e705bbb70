/**
 * 聊天滚动功能测试工具
 */

export class ScrollTester {
  private static instance: ScrollTester | null = null

  static getInstance(): ScrollTester {
    if (!this.instance) {
      this.instance = new ScrollTester()
    }
    return this.instance
  }

  /**
   * 测试滚动到底部功能
   */
  async testScrollToBottom(): Promise<boolean> {
    console.log('🧪 开始测试滚动到底部功能...')
    
    try {
      const messageList = document.querySelector('.message-list') as HTMLElement
      if (!messageList) {
        console.error('❌ 未找到消息列表容器')
        return false
      }

      const messagesContainer = messageList.querySelector('.messages') as HTMLElement
      if (!messagesContainer) {
        console.error('❌ 未找到消息容器')
        return false
      }

      // 记录初始状态
      const initialScrollTop = messageList.scrollTop
      const scrollHeight = messagesContainer.scrollHeight
      const clientHeight = messageList.clientHeight
      const maxScrollTop = scrollHeight - clientHeight

      console.log('📊 滚动状态:', {
        initialScrollTop,
        scrollHeight,
        clientHeight,
        maxScrollTop,
        isAtBottom: Math.abs(initialScrollTop - maxScrollTop) <= 5
      })

      // 测试滚动到顶部
      messageList.scrollTop = 0
      await new Promise(resolve => setTimeout(resolve, 100))

      // 测试滚动到底部
      messageList.scrollTop = maxScrollTop
      await new Promise(resolve => setTimeout(resolve, 100))

      const finalScrollTop = messageList.scrollTop
      const isAtBottom = Math.abs(finalScrollTop - maxScrollTop) <= 5

      console.log('✅ 滚动测试结果:', {
        finalScrollTop,
        maxScrollTop,
        difference: Math.abs(finalScrollTop - maxScrollTop),
        isAtBottom
      })

      return isAtBottom

    } catch (error) {
      console.error('❌ 滚动测试失败:', error)
      return false
    }
  }

  /**
   * 测试发送消息后的滚动
   */
  async testScrollAfterMessage(): Promise<boolean> {
    console.log('🧪 测试发送消息后的滚动...')
    
    try {
      const messageList = document.querySelector('.message-list') as HTMLElement
      const messagesContainer = messageList?.querySelector('.messages') as HTMLElement
      
      if (!messageList || !messagesContainer) {
        console.error('❌ 未找到必要的DOM元素')
        return false
      }

      // 记录发送前的状态
      const beforeScrollTop = messageList.scrollTop
      const beforeScrollHeight = messagesContainer.scrollHeight

      console.log('📝 发送前状态:', {
        scrollTop: beforeScrollTop,
        scrollHeight: beforeScrollHeight
      })

      // 模拟添加新消息
      const newMessage = document.createElement('div')
      newMessage.className = 'message-item own-message'
      newMessage.innerHTML = `
        <div class="message-content">
          <div class="message-bubble message-bubble--self">
            测试消息 ${Date.now()}
          </div>
        </div>
      `
      messagesContainer.appendChild(newMessage)

      // 等待DOM更新
      await new Promise(resolve => setTimeout(resolve, 50))

      // 模拟发送消息后的滚动逻辑
      const afterScrollHeight = messagesContainer.scrollHeight
      const maxScrollTop = afterScrollHeight - messageList.clientHeight
      messageList.scrollTop = maxScrollTop

      await new Promise(resolve => setTimeout(resolve, 100))

      const finalScrollTop = messageList.scrollTop
      const isAtBottom = Math.abs(finalScrollTop - maxScrollTop) <= 5

      console.log('📤 发送后状态:', {
        beforeScrollHeight,
        afterScrollHeight,
        maxScrollTop,
        finalScrollTop,
        isAtBottom
      })

      // 清理测试消息
      newMessage.remove()

      return isAtBottom

    } catch (error) {
      console.error('❌ 发送消息滚动测试失败:', error)
      return false
    }
  }

  /**
   * 测试键盘弹出时的滚动调整
   */
  async testKeyboardScroll(): Promise<boolean> {
    console.log('🧪 测试键盘弹出时的滚动调整...')
    
    try {
      const messageList = document.querySelector('.message-list') as HTMLElement
      if (!messageList) {
        console.error('❌ 未找到消息列表')
        return false
      }

      // 模拟键盘弹出前的状态
      const beforeHeight = messageList.clientHeight
      const beforeScrollTop = messageList.scrollTop

      // 模拟键盘弹出（减少可视高度）
      const keyboardHeight = 300
      messageList.style.height = `calc(100vh - 46px - 80px - ${keyboardHeight}px)`

      await new Promise(resolve => setTimeout(resolve, 100))

      const afterHeight = messageList.clientHeight
      const afterScrollTop = messageList.scrollTop

      console.log('⌨️ 键盘弹出测试:', {
        beforeHeight,
        afterHeight,
        beforeScrollTop,
        afterScrollTop,
        keyboardHeight,
        heightDifference: beforeHeight - afterHeight
      })

      // 恢复原始高度
      messageList.style.height = 'calc(100vh - 46px - 80px)'

      return true

    } catch (error) {
      console.error('❌ 键盘滚动测试失败:', error)
      return false
    }
  }

  /**
   * 运行完整的滚动测试套件
   */
  async runFullScrollTest(): Promise<boolean> {
    console.log('🚀 开始运行完整的滚动测试套件...')
    
    let allTestsPassed = true

    // 1. 基础滚动测试
    const basicScrollTest = await this.testScrollToBottom()
    if (!basicScrollTest) {
      allTestsPassed = false
    }

    // 2. 发送消息滚动测试
    const messageScrollTest = await this.testScrollAfterMessage()
    if (!messageScrollTest) {
      allTestsPassed = false
    }

    // 3. 键盘滚动测试
    const keyboardScrollTest = await this.testKeyboardScroll()
    if (!keyboardScrollTest) {
      allTestsPassed = false
    }

    if (allTestsPassed) {
      console.log('🎉 所有滚动测试通过！')
    } else {
      console.log('❌ 部分滚动测试失败，请检查相关功能')
    }

    return allTestsPassed
  }

  /**
   * 获取当前滚动状态信息
   */
  getScrollStatus(): any {
    const messageList = document.querySelector('.message-list') as HTMLElement
    const messagesContainer = messageList?.querySelector('.messages') as HTMLElement
    
    if (!messageList || !messagesContainer) {
      return { error: '未找到滚动容器' }
    }

    const scrollTop = messageList.scrollTop
    const scrollHeight = messagesContainer.scrollHeight
    const clientHeight = messageList.clientHeight
    const maxScrollTop = scrollHeight - clientHeight
    const distanceFromBottom = maxScrollTop - scrollTop
    const isAtBottom = distanceFromBottom <= 5

    return {
      scrollTop,
      scrollHeight,
      clientHeight,
      maxScrollTop,
      distanceFromBottom,
      isAtBottom,
      scrollPercentage: Math.round((scrollTop / maxScrollTop) * 100)
    }
  }
}

// 导出便捷方法
export const testChatScroll = () => {
  return ScrollTester.getInstance().runFullScrollTest()
}

export const getScrollStatus = () => {
  return ScrollTester.getInstance().getScrollStatus()
}

// 在开发环境下暴露到全局
if (import.meta.env.DEV) {
  (window as any).testChatScroll = testChatScroll
  (window as any).getScrollStatus = getScrollStatus
  (window as any).ScrollTester = ScrollTester
}
