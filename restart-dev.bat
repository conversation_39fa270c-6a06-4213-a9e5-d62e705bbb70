@echo off
echo 正在重启开发服务器...
echo.

echo 1. 停止当前服务器 (如果正在运行)
taskkill /f /im node.exe 2>nul

echo 2. 清理缓存
if exist node_modules\.vite rmdir /s /q node_modules\.vite
if exist dist rmdir /s /q dist

echo 3. 检查后端服务器状态
call check-backend.bat

echo 4. 启动开发服务器
echo 🚀 前端将在 http://localhost:5173 启动
echo 🔗 后端应在 https://localhost:7250 运行
echo 📡 SignalR Hub: https://localhost:7250/chatHub
echo.
echo 如果后端未启动，请运行: start-backend.bat
echo.
npm run dev

pause
