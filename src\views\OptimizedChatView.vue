<template>
  <div class="chat-container" :class="{ 'keyboard-open': isKeyboardOpen }">
    <!-- 导航栏 -->
    <van-nav-bar
      :title="chatTitle"
      left-text="返回"
      left-arrow
      @click-left="goBack"
      fixed
      placeholder
      class="chat-navbar"
    >
      <template #right>
        <van-icon name="ellipsis" size="18" @click="showChatMenu = true" />
      </template>
    </van-nav-bar>

    <!-- 消息列表容器 -->
    <div 
      class="message-list-container" 
      ref="messageListContainer"
      :style="{ height: containerHeight }"
    >
      <!-- 消息列表 -->
      <div 
        class="message-list" 
        ref="messageListRef"
        @scroll="onScroll"
      >
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <div class="messages" ref="messagesContainer">
            <!-- 空状态 -->
            <div v-if="messages.length === 0" class="empty-messages">
              <div class="empty-icon">💬</div>
              <div class="empty-text">暂无聊天记录</div>
              <div class="empty-hint">发送一条消息开始聊天吧</div>
            </div>

            <!-- 消息列表 -->
            <div
              v-for="message in messages"
              :key="message.id"
              :class="[
                'message-item',
                { 'own-message': message.isOwn }
              ]"
            >
              <!-- 他人消息头像 -->
              <div class="message-avatar">
                <van-image
                  v-if="!message.isOwn"
                  :src="message.avatar || defaultAvatar"
                  class="avatar"
                  round
                  width="40"
                  height="40"
                  fit="cover"
                />
              </div>

              <!-- 消息内容 -->
              <div class="message-content">
                <!-- 消息气泡 -->
                <div
                  :class="[
                    'message-bubble',
                    message.isOwn ? 'message-bubble--self' : 'message-bubble--other'
                  ]"
                >
                  <!-- 消息内容 -->
                  <div class="message-text">
                    {{ message.content }}
                  </div>
                </div>

                <!-- 消息状态和时间 -->
                <div class="message-meta">
                  <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                  <van-icon 
                    v-if="message.isOwn && message.status === 'sending'" 
                    name="clock-o" 
                    size="12" 
                    color="#999"
                  />
                  <van-icon 
                    v-if="message.isOwn && message.status === 'sent'" 
                    name="success" 
                    size="12" 
                    color="#07C160"
                  />
                  <van-icon 
                    v-if="message.isOwn && message.status === 'failed'" 
                    name="warning-o" 
                    size="12" 
                    color="#ee0a24"
                    @click="retryMessage(message)"
                  />
                </div>
              </div>

              <!-- 自己消息头像 -->
              <div class="message-avatar">
                <van-image
                  v-if="message.isOwn"
                  :src="userAvatar || defaultAvatar"
                  class="avatar"
                  round
                  width="40"
                  height="40"
                  fit="cover"
                />
              </div>
            </div>
          </div>
        </van-pull-refresh>
      </div>

      <!-- 滚动到底部按钮 -->
      <transition name="fade">
        <div 
          v-show="showScrollToBottomBtn" 
          class="scroll-to-bottom-btn"
          @click="scrollToBottom(true)"
        >
          <van-icon name="arrow-down" size="16" />
          <span v-if="unreadCount > 0" class="unread-badge">{{ unreadCount }}</span>
        </div>
      </transition>
    </div>

    <!-- 输入区域 -->
    <div class="input-area" ref="inputAreaRef">
      <div class="input-container">
        <!-- 表情按钮 -->
        <van-icon 
          name="smile-o" 
          size="24" 
          class="input-icon"
          @click="toggleEmojiPicker"
        />

        <!-- 输入框 -->
        <van-field
          v-model="inputMessage"
          placeholder="输入消息..."
          class="message-input"
          autosize
          :maxlength="1000"
          @focus="onInputFocus"
          @blur="onInputBlur"
          @keyup.enter="sendMessage"
        />

        <!-- 发送按钮 -->
        <van-button
          type="primary"
          size="small"
          :loading="sending"
          @click="sendMessage"
          :disabled="!inputMessage.trim()"
        >
          发送
        </van-button>
      </div>

      <!-- 表情选择器 -->
      <transition name="slide-up">
        <div v-show="showEmojiPicker" class="simple-emoji-picker">
          <div class="emoji-grid">
            <span
              v-for="emoji in commonEmojis"
              :key="emoji"
              class="emoji-item"
              @click="onEmojiSelect(emoji)"
            >
              {{ emoji }}
            </span>
          </div>
          <div class="emoji-close" @click="showEmojiPicker = false">
            <van-icon name="cross" />
          </div>
        </div>
      </transition>
    </div>

    <!-- 聊天菜单 -->
    <van-action-sheet
      v-model:show="showChatMenu"
      :actions="chatMenuActions"
      @select="onChatMenuSelect"
      cancel-text="取消"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from 'vant'
import { createKeyboardAdapter, type KeyboardState } from '@/utils/keyboardAdapter'
// import LongMessage from '@/components/LongMessage.vue'
// import EmojiPicker from '@/components/EmojiPicker.vue'

// 路由和基础状态
const route = useRoute()
const router = useRouter()
const chatId = computed(() => route.params.id as string)
const chatType = computed(() => route.query.type as string || 'private')

// DOM引用
const messageListContainer = ref<HTMLElement>()
const messageListRef = ref<HTMLElement>()
const messagesContainer = ref<HTMLElement>()
const inputAreaRef = ref<HTMLElement>()

// 基础状态
const refreshing = ref(false)
const sending = ref(false)
const inputMessage = ref('')
const showChatMenu = ref(false)
const showEmojiPicker = ref(false)

// 滚动相关状态
const isKeyboardOpen = ref(false)
const showScrollToBottomBtn = ref(false)
const unreadCount = ref(0)
const lastScrollTop = ref(0)
const isUserScrolling = ref(false)

// 聊天数据
const chatTitle = ref('聊天')
const userAvatar = ref('')
const defaultAvatar = ref('https://via.placeholder.com/40')

// 消息接口
interface Message {
  id: string
  content: string
  isOwn: boolean
  avatar?: string
  timestamp: Date
  status: 'sending' | 'sent' | 'failed'
}

// 示例消息数据
const messages = ref<Message[]>([
  {
    id: '1',
    content: '你好！这是一条测试消息',
    isOwn: false,
    timestamp: new Date(Date.now() - 60000),
    status: 'sent'
  },
  {
    id: '2', 
    content: '你好！我收到了',
    isOwn: true,
    timestamp: new Date(Date.now() - 30000),
    status: 'sent'
  }
])

// 计算属性
const containerHeight = computed(() => {
  if (isKeyboardOpen.value) {
    return `calc(100vh - 46px - ${keyboardHeight.value}px)`
  }
  return 'calc(100vh - 46px - 80px)'
})

// 键盘高度
const keyboardHeight = ref(80)

// 常用表情
const commonEmojis = [
  '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂',
  '🙂', '🙃', '😉', '😊', '😇', '🥰', '😍', '🤩',
  '😘', '😗', '😚', '😙', '😋', '😛', '😜', '🤪',
  '😝', '🤑', '🤗', '🤭', '🤫', '🤔', '🤐', '🤨',
  '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥',
  '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢',
  '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙',
  '👈', '👉', '👆', '👇', '☝️', '✋', '🤚', '🖐️',
  '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍'
]

// 聊天菜单
const chatMenuActions = [
  { name: '查看聊天信息', value: 'info' },
  { name: '清空聊天记录', value: 'clear' },
  { name: '删除聊天', value: 'delete' }
]

// ==================== 核心滚动逻辑 ====================

/**
 * 智能滚动管理器
 */
class SmartScrollManager {
  private container: HTMLElement | null = null
  private messageList: HTMLElement | null = null
  private isUserScrolling = false
  private scrollTimeout: NodeJS.Timeout | null = null

  constructor() {
    this.bindEvents()
  }

  // 初始化容器引用
  init(container: HTMLElement, messageList: HTMLElement) {
    this.container = container
    this.messageList = messageList
  }

  // 绑定事件
  private bindEvents() {
    // 监听滚动事件
    document.addEventListener('touchstart', this.onTouchStart.bind(this))
    document.addEventListener('touchend', this.onTouchEnd.bind(this))
  }

  private onTouchStart() {
    this.isUserScrolling = true
  }

  private onTouchEnd() {
    // 延迟重置用户滚动状态
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout)
    }
    this.scrollTimeout = setTimeout(() => {
      this.isUserScrolling = false
    }, 1000)
  }

  // 检查是否在底部附近
  isNearBottom(threshold = 100): boolean {
    if (!this.messageList) return true

    const { scrollTop, scrollHeight, clientHeight } = this.messageList
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight

    return distanceFromBottom <= threshold
  }

  // 检查是否完全在底部
  isAtBottom(threshold = 5): boolean {
    if (!this.messageList) return true

    const { scrollTop, scrollHeight, clientHeight } = this.messageList
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight

    return distanceFromBottom <= threshold
  }

  // 精确计算滚动位置
  calculateScrollPosition(): number {
    if (!this.messageList || !this.container) return 0

    const messagesContainer = this.messageList.querySelector('.messages') as HTMLElement
    if (!messagesContainer) return 0

    const contentHeight = messagesContainer.scrollHeight
    const containerHeight = this.messageList.clientHeight

    return Math.max(0, contentHeight - containerHeight)
  }

  // 滚动到底部
  scrollToBottom(smooth = false): Promise<void> {
    return new Promise((resolve) => {
      if (!this.messageList) {
        resolve()
        return
      }

      const targetScrollTop = this.calculateScrollPosition()

      this.messageList.scrollTo({
        top: targetScrollTop,
        behavior: smooth ? 'smooth' : 'auto'
      })

      // 验证滚动结果
      const checkScroll = () => {
        if (!this.messageList) {
          resolve()
          return
        }

        const actualScrollTop = this.messageList.scrollTop
        const difference = Math.abs(actualScrollTop - targetScrollTop)

        if (difference > 5) {
          // 如果滚动不精确，进行微调
          this.messageList.scrollTop = targetScrollTop
        }

        resolve()
      }

      if (smooth) {
        setTimeout(checkScroll, 300)
      } else {
        setTimeout(checkScroll, 50)
      }
    })
  }

  // 智能滚动：根据用户状态决定是否滚动
  smartScroll(): Promise<void> {
    if (this.isUserScrolling) {
      console.log('用户正在滚动，跳过自动滚动')
      return Promise.resolve()
    }

    if (this.isNearBottom()) {
      console.log('用户在底部附近，执行自动滚动')
      return this.scrollToBottom(true)
    } else {
      console.log('用户不在底部，跳过自动滚动')
      return Promise.resolve()
    }
  }

  // 强制滚动（用于发送消息）
  forceScroll(): Promise<void> {
    console.log('强制滚动到底部')
    return this.scrollToBottom(false)
  }

  // 获取滚动状态
  getScrollStatus() {
    if (!this.messageList) return null

    const { scrollTop, scrollHeight, clientHeight } = this.messageList
    const maxScrollTop = scrollHeight - clientHeight
    const distanceFromBottom = maxScrollTop - scrollTop

    return {
      scrollTop,
      scrollHeight,
      clientHeight,
      maxScrollTop,
      distanceFromBottom,
      isAtBottom: this.isAtBottom(),
      isNearBottom: this.isNearBottom(),
      scrollPercentage: maxScrollTop > 0 ? Math.round((scrollTop / maxScrollTop) * 100) : 100
    }
  }

  // 清理资源
  destroy() {
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout)
    }
    document.removeEventListener('touchstart', this.onTouchStart.bind(this))
    document.removeEventListener('touchend', this.onTouchEnd.bind(this))
  }
}

// 创建滚动管理器实例
const scrollManager = new SmartScrollManager()

// ==================== 键盘适配逻辑 ====================

// 创建键盘适配器实例
const keyboardAdapter = createKeyboardAdapter({
  threshold: 150,
  debounceTime: 100,
  enableAutoScroll: true
})

// ==================== 消息处理方法 ====================

// 格式化时间
const formatTime = (timestamp: Date): string => {
  const now = new Date()
  const diff = now.getTime() - timestamp.getTime()

  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return timestamp.toLocaleDateString()
  }
}

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim() || sending.value) return

  const content = inputMessage.value.trim()
  inputMessage.value = ''
  sending.value = true

  // 创建新消息
  const newMessage: Message = {
    id: Date.now().toString(),
    content,
    isOwn: true,
    timestamp: new Date(),
    status: 'sending'
  }

  // 添加到消息列表
  messages.value.push(newMessage)

  try {
    // 发送消息后立即滚动到底部
    await nextTick()
    await scrollManager.forceScroll()

    // 模拟发送过程
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新消息状态
    newMessage.status = 'sent'

  } catch (error) {
    console.error('发送消息失败:', error)
    newMessage.status = 'failed'
    showToast('发送失败')
  } finally {
    sending.value = false
  }
}

// 重试发送消息
const retryMessage = async (message: Message) => {
  message.status = 'sending'

  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.status = 'sent'
    showToast('重发成功')
  } catch (error) {
    message.status = 'failed'
    showToast('重发失败')
  }
}

// 接收新消息（模拟）
const receiveMessage = async (content: string) => {
  const newMessage: Message = {
    id: Date.now().toString(),
    content,
    isOwn: false,
    timestamp: new Date(),
    status: 'sent'
  }

  messages.value.push(newMessage)

  // 智能滚动：只有在用户接近底部时才自动滚动
  await nextTick()
  await scrollManager.smartScroll()

  // 如果用户不在底部，显示未读消息提示
  if (!scrollManager.isNearBottom()) {
    unreadCount.value++
    showScrollToBottomBtn.value = true
  }
}

// ==================== 事件处理方法 ====================

// 返回上一页
const goBack = () => {
  router.back()
}

// 滚动事件处理
const onScroll = () => {
  if (!messageListRef.value) return

  const scrollStatus = scrollManager.getScrollStatus()
  if (!scrollStatus) return

  // 更新滚动到底部按钮显示状态
  showScrollToBottomBtn.value = !scrollStatus.isNearBottom && messages.value.length > 0

  // 如果滚动到底部，清除未读计数
  if (scrollStatus.isAtBottom) {
    unreadCount.value = 0
    showScrollToBottomBtn.value = false
  }

  // 记录最后滚动位置
  lastScrollTop.value = scrollStatus.scrollTop
}

// 滚动到底部（手动点击）
const scrollToBottom = async (smooth = true) => {
  await scrollManager.scrollToBottom(smooth)
  unreadCount.value = 0
  showScrollToBottomBtn.value = false
}

// 刷新消息
const onRefresh = async () => {
  // 模拟加载历史消息
  await new Promise(resolve => setTimeout(resolve, 1000))

  // 添加一些历史消息
  const historyMessages: Message[] = [
    {
      id: `history-${Date.now()}`,
      content: '这是一条历史消息',
      isOwn: Math.random() > 0.5,
      timestamp: new Date(Date.now() - Math.random() * 86400000),
      status: 'sent'
    }
  ]

  messages.value.unshift(...historyMessages)
  refreshing.value = false

  showToast('加载完成')
}

// 输入框焦点事件
const onInputFocus = () => {
  // 如果用户在底部附近，滚动到底部
  if (scrollManager.isNearBottom()) {
    setTimeout(() => {
      scrollManager.scrollToBottom(true)
    }, 300)
  }
}

const onInputBlur = () => {
  // 输入框失去焦点时的处理
}

// 表情选择器
const toggleEmojiPicker = () => {
  showEmojiPicker.value = !showEmojiPicker.value
}

const onEmojiSelect = (emoji: string) => {
  inputMessage.value += emoji
  showEmojiPicker.value = false
}

// 长消息展开
const onMessageExpand = (expanded: boolean) => {
  // 消息展开后，如果用户在底部，保持在底部
  if (expanded && scrollManager.isNearBottom()) {
    nextTick(() => {
      scrollManager.scrollToBottom(true)
    })
  }
}

// 聊天菜单
const onChatMenuSelect = (action: any) => {
  switch (action.value) {
    case 'info':
      showToast('查看聊天信息')
      break
    case 'clear':
      messages.value = []
      showToast('聊天记录已清空')
      break
    case 'delete':
      showToast('删除聊天')
      break
  }
  showChatMenu.value = false
}

// ==================== 生命周期钩子 ====================

onMounted(async () => {
  // 初始化滚动管理器
  if (messageListContainer.value && messageListRef.value) {
    scrollManager.init(messageListContainer.value, messageListRef.value)
  }

  // 监听键盘状态变化
  keyboardAdapter.onStateChange((state: KeyboardState) => {
    isKeyboardOpen.value = state.isOpen
    keyboardHeight.value = state.isOpen ? state.height : 80

    // 如果用户在底部附近，键盘状态变化时保持在底部
    if (scrollManager.isNearBottom()) {
      setTimeout(() => {
        scrollManager.scrollToBottom(true)
      }, 100)
    }
  })

  // 初始滚动到底部
  await nextTick()
  await scrollManager.scrollToBottom(false)

  // 模拟接收消息（测试用）
  setTimeout(() => {
    receiveMessage('这是一条新收到的消息，用于测试智能滚动功能')
  }, 3000)

  setTimeout(() => {
    receiveMessage('这是另一条消息，如果你在查看历史消息，我不会强制滚动')
  }, 6000)
})

onUnmounted(() => {
  // 清理资源
  scrollManager.destroy()
  keyboardAdapter.destroy()
})

// 监听消息变化
watch(messages, async () => {
  await nextTick()

  // 如果是新增消息且用户在底部附近，自动滚动
  if (scrollManager.isNearBottom()) {
    await scrollManager.scrollToBottom(true)
  }
}, { deep: true })
</script>

<style scoped>
/* ==================== 基础布局 ==================== */
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  position: relative;
  overflow: hidden;
}

.chat-navbar {
  z-index: 1000;
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
}

/* ==================== 消息列表容器 ==================== */
.message-list-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  transition: height 0.3s ease;
}

.message-list {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
  padding-bottom: 20px;

  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: auto; /* 手动控制滚动行为 */

  /* 自定义滚动条 */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.message-list::-webkit-scrollbar {
  width: 3px;
}

.message-list::-webkit-scrollbar-track {
  background: transparent;
}

.message-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.messages {
  min-height: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* ==================== 空状态 ==================== */
.empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.empty-hint {
  font-size: 14px;
  opacity: 0.7;
}

/* ==================== 消息项 ==================== */
.message-item {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  margin-bottom: 4px;
}

.message-item.own-message {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  flex-shrink: 0;
}

.avatar {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-content {
  flex: 1;
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.own-message .message-content {
  align-items: flex-end;
}

/* ==================== 消息气泡 ==================== */
.message-bubble {
  padding: 12px 16px;
  border-radius: 8px;
  word-wrap: break-word;
  line-height: 1.4;
  position: relative;
  max-width: 100%;

  /* 添加微信风格的阴影 */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-bubble--self {
  background: #95EC69;
  color: #333;
}

.message-bubble--self::after {
  content: '';
  position: absolute;
  right: -6px;
  bottom: 8px;
  width: 0;
  height: 0;
  border-left: 6px solid #95EC69;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

.message-bubble--other {
  background: #fff;
  color: #333;
}

.message-bubble--other::before {
  content: '';
  position: absolute;
  left: -6px;
  bottom: 8px;
  width: 0;
  height: 0;
  border-right: 6px solid #fff;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
}

/* ==================== 消息元信息 ==================== */
.message-meta {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: #999;
}

.own-message .message-meta {
  flex-direction: row-reverse;
}

.message-time {
  white-space: nowrap;
}

/* ==================== 滚动到底部按钮 ==================== */
.scroll-to-bottom-btn {
  position: absolute;
  right: 16px;
  bottom: 20px;
  width: 40px;
  height: 40px;
  background: #fff;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  z-index: 100;
}

.scroll-to-bottom-btn:hover {
  background: #f5f5f5;
}

.scroll-to-bottom-btn:active {
  transform: scale(0.95);
}

.unread-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ee0a24;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

/* ==================== 输入区域 ==================== */
.input-area {
  background: #fff;
  border-top: 1px solid #e5e5e5;
  padding: 12px;
  transition: all 0.3s ease;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.input-icon {
  color: #666;
  cursor: pointer;
  padding: 8px;
}

.input-icon:hover {
  color: #07C160;
}

.message-input {
  flex: 1;
  background: #f5f5f5;
  border-radius: 6px;
}

.message-input :deep(.van-field__control) {
  min-height: 36px;
  max-height: 100px;
  line-height: 1.4;
}

/* ==================== 表情选择器 ==================== */
.simple-emoji-picker {
  position: absolute;
  bottom: 80px;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 12px 12px 0 0;
  border: 1px solid #e5e5e5;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
  padding: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.emoji-item {
  font-size: 24px;
  padding: 8px;
  text-align: center;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  user-select: none;
}

.emoji-item:hover {
  background: #f5f5f5;
  transform: scale(1.2);
}

.emoji-item:active {
  transform: scale(1.1);
}

.emoji-close {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 8px;
  border-radius: 50%;
  cursor: pointer;
  color: #666;
  transition: all 0.2s;
}

.emoji-close:hover {
  background: #f5f5f5;
  color: #333;
}

/* ==================== 动画效果 ==================== */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

/* ==================== 键盘适配 ==================== */
.keyboard-open .message-list-container {
  transition: height 0.3s ease;
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 375px) {
  .message-bubble {
    padding: 10px 14px;
    font-size: 14px;
  }

  .message-content {
    max-width: 75%;
  }
}

@media (min-width: 768px) {
  .message-content {
    max-width: 60%;
  }
}

/* ==================== 性能优化 ==================== */
.message-item {
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: transform;
}

.message-list {
  /* 优化滚动性能 */
  contain: layout style paint;
}
</style>
