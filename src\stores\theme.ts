import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export type ThemeType = 'light' | 'dark' | 'wechat' | 'business'

export interface ThemeConfig {
  name: string
  displayName: string
  colors: {
    primary: string
    background: string
    surface: string
    text: string
    textSecondary: string
    border: string
    success: string
    warning: string
    danger: string
    chatBubbleSelf: string
    chatBubbleOther: string
    navBar: string
    tabBar: string
  }
}

const themes: Record<ThemeType, ThemeConfig> = {
  light: {
    name: 'light',
    displayName: '浅色模式',
    colors: {
      primary: '#07c160',
      background: '#f7f8fa',
      surface: '#ffffff',
      text: '#323233',
      textSecondary: '#969799',
      border: '#ebedf0',
      success: '#07c160',
      warning: '#ff976a',
      danger: '#ee0a24',
      chatBubbleSelf: '#07c160',
      chatBubbleOther: '#ffffff',
      navBar: '#ffffff',
      tabBar: '#ffffff'
    }
  },
  dark: {
    name: 'dark',
    displayName: '深色模式',
    colors: {
      primary: '#07c160',
      background: '#1a1a1a',
      surface: '#2a2a2a',
      text: '#ffffff',
      textSecondary: '#cccccc',
      border: '#333333',
      success: '#07c160',
      warning: '#ff976a',
      danger: '#ee0a24',
      chatBubbleSelf: '#07c160',
      chatBubbleOther: '#3a3a3a',
      navBar: '#2a2a2a',
      tabBar: '#2a2a2a'
    }
  },
  wechat: {
    name: 'wechat',
    displayName: '微信绿',
    colors: {
      primary: '#07c160',
      background: '#ededed',
      surface: '#ffffff',
      text: '#000000',
      textSecondary: '#888888',
      border: '#d9d9d9',
      success: '#07c160',
      warning: '#fa9d3b',
      danger: '#f56c6c',
      chatBubbleSelf: '#95ec69',
      chatBubbleOther: '#ffffff',
      navBar: '#f7f7f7',
      tabBar: '#f7f7f7'
    }
  },
  business: {
    name: 'business',
    displayName: '商务蓝',
    colors: {
      primary: '#1890ff',
      background: '#f0f2f5',
      surface: '#ffffff',
      text: '#262626',
      textSecondary: '#8c8c8c',
      border: '#d9d9d9',
      success: '#52c41a',
      warning: '#faad14',
      danger: '#f5222d',
      chatBubbleSelf: '#1890ff',
      chatBubbleOther: '#ffffff',
      navBar: '#ffffff',
      tabBar: '#ffffff'
    }
  }
}

export const useThemeStore = defineStore('theme', () => {
  // 状态
  const currentTheme = ref<ThemeType>('wechat')
  
  // 计算属性
  const theme = computed(() => themes[currentTheme.value])
  const themeList = computed(() => Object.values(themes))
  
  // 初始化主题
  const initTheme = () => {
    const savedTheme = localStorage.getItem('theme') as ThemeType
    if (savedTheme && themes[savedTheme]) {
      currentTheme.value = savedTheme
    }
    applyTheme()
  }
  
  // 应用主题
  const applyTheme = () => {
    const root = document.documentElement
    const colors = theme.value.colors
    
    // 设置CSS变量
    root.style.setProperty('--theme-primary', colors.primary)
    root.style.setProperty('--theme-background', colors.background)
    root.style.setProperty('--theme-surface', colors.surface)
    root.style.setProperty('--theme-text', colors.text)
    root.style.setProperty('--theme-text-secondary', colors.textSecondary)
    root.style.setProperty('--theme-border', colors.border)
    root.style.setProperty('--theme-success', colors.success)
    root.style.setProperty('--theme-warning', colors.warning)
    root.style.setProperty('--theme-danger', colors.danger)
    root.style.setProperty('--theme-chat-bubble-self', colors.chatBubbleSelf)
    root.style.setProperty('--theme-chat-bubble-other', colors.chatBubbleOther)
    root.style.setProperty('--theme-nav-bar', colors.navBar)
    root.style.setProperty('--theme-tab-bar', colors.tabBar)
    
    // 覆盖Vant的CSS变量
    root.style.setProperty('--van-primary-color', colors.primary)
    root.style.setProperty('--van-background-color', colors.background)
    root.style.setProperty('--van-background-color-light', colors.surface)
    root.style.setProperty('--van-text-color', colors.text)
    root.style.setProperty('--van-text-color-2', colors.textSecondary)
    root.style.setProperty('--van-border-color', colors.border)
    root.style.setProperty('--van-success-color', colors.success)
    root.style.setProperty('--van-warning-color', colors.warning)
    root.style.setProperty('--van-danger-color', colors.danger)
    root.style.setProperty('--van-nav-bar-background-color', colors.navBar)
    root.style.setProperty('--van-tabbar-background-color', colors.tabBar)
    
    // 设置body背景色
    document.body.style.backgroundColor = colors.background
    
    // 添加主题类名
    document.body.className = document.body.className.replace(/theme-\w+/g, '')
    document.body.classList.add(`theme-${currentTheme.value}`)
  }
  
  // 切换主题
  const setTheme = (themeName: ThemeType) => {
    currentTheme.value = themeName
    localStorage.setItem('theme', themeName)
    applyTheme()
  }
  
  // 切换到下一个主题
  const toggleTheme = () => {
    const themeNames = Object.keys(themes) as ThemeType[]
    const currentIndex = themeNames.indexOf(currentTheme.value)
    const nextIndex = (currentIndex + 1) % themeNames.length
    setTheme(themeNames[nextIndex])
  }
  
  return {
    // 状态
    currentTheme,
    
    // 计算属性
    theme,
    themeList,
    
    // 方法
    initTheme,
    applyTheme,
    setTheme,
    toggleTheme
  }
})
