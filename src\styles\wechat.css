/* 微信风格设计系统 */

/* 字体系统 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');

:root {
  /* 微信标准色彩 */
  --wechat-primary: #07C160;
  --wechat-primary-light: #95EC69;
  --wechat-primary-dark: #06AD56;
  --wechat-background: #EDEDED;
  --wechat-surface: #FFFFFF;
  --wechat-text-primary: #000000;
  --wechat-text-secondary: #888888;
  --wechat-text-tertiary: #B2B2B2;
  --wechat-border: #E5E5E5;
  --wechat-divider: #D9D9D9;
  
  /* 间距系统 */
  --wechat-spacing-xs: 4px;
  --wechat-spacing-sm: 8px;
  --wechat-spacing-md: 12px;
  --wechat-spacing-lg: 16px;
  --wechat-spacing-xl: 20px;
  --wechat-spacing-xxl: 24px;
  
  /* 圆角系统 */
  --wechat-radius-xs: 4px;
  --wechat-radius-sm: 6px;
  --wechat-radius-md: 8px;
  --wechat-radius-lg: 12px;
  --wechat-radius-xl: 16px;
  --wechat-radius-bubble: 18px;
  
  /* 阴影系统 */
  --wechat-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --wechat-shadow-md: 0 2px 4px rgba(0, 0, 0, 0.08);
  --wechat-shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.12);
  --wechat-shadow-bubble: 0 0.8px 2px rgba(0, 0, 0, 0.08);
  
  /* 动画时长 */
  --wechat-duration-fast: 0.15s;
  --wechat-duration-normal: 0.3s;
  --wechat-duration-slow: 0.5s;
  
  /* 字体系统 */
  --wechat-font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Helvetica Neue', STHeiti, 'Microsoft Yahei', Tahoma, Simsun, sans-serif;
  --wechat-font-size-xs: 10px;
  --wechat-font-size-sm: 12px;
  --wechat-font-size-md: 14px;
  --wechat-font-size-lg: 16px;
  --wechat-font-size-xl: 18px;
  --wechat-font-size-xxl: 20px;
  
  /* 行高系统 */
  --wechat-line-height-tight: 1.2;
  --wechat-line-height-normal: 1.4;
  --wechat-line-height-relaxed: 1.6;
}

/* 基础字体设置 */
body {
  font-family: var(--wechat-font-family);
  font-size: var(--wechat-font-size-md);
  line-height: var(--wechat-line-height-normal);
  color: var(--wechat-text-primary);
  background-color: var(--wechat-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 微信风格消息气泡 */
.wechat-message-bubble {
  border-radius: var(--wechat-radius-bubble);
  padding: var(--wechat-spacing-md) var(--wechat-spacing-lg);
  box-shadow: var(--wechat-shadow-bubble);
  position: relative;
  max-width: 100%;
  word-wrap: break-word;
  font-size: var(--wechat-font-size-lg);
  line-height: var(--wechat-line-height-normal);
  
  /* 消息发送/接收动画 */
  animation: messageSlideIn var(--wechat-duration-normal) ease-out;
  transform-origin: bottom;
}

.wechat-message-bubble--self {
  background: var(--wechat-primary-light);
  color: var(--wechat-text-primary);
  margin-left: auto;
}

.wechat-message-bubble--other {
  background: var(--wechat-surface);
  color: var(--wechat-text-primary);
  border: 1px solid var(--wechat-border);
}

/* 消息气泡尾巴 */
.wechat-message-bubble::before {
  content: '';
  position: absolute;
  top: var(--wechat-spacing-md);
  width: 0;
  height: 0;
  border: 6px solid transparent;
}

.wechat-message-bubble--self::before {
  right: -6px;
  border-left-color: var(--wechat-primary-light);
}

.wechat-message-bubble--other::before {
  left: -6px;
  border-right-color: var(--wechat-surface);
}

/* 消息动画 */
@keyframes messageSlideIn {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 消息时间戳 */
.wechat-message-time {
  font-size: var(--wechat-font-size-xs);
  color: var(--wechat-text-tertiary);
  margin-top: var(--wechat-spacing-xs);
  opacity: 0.7;
  transition: opacity var(--wechat-duration-fast);
}

/* 日期分隔线 */
.wechat-date-divider {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: var(--wechat-spacing-lg) 0;
  position: relative;
}

.wechat-date-divider::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--wechat-divider);
  opacity: 0.3;
}

.wechat-date-text {
  background: var(--wechat-background);
  padding: var(--wechat-spacing-xs) var(--wechat-spacing-md);
  border-radius: var(--wechat-radius-lg);
  font-size: var(--wechat-font-size-sm);
  color: var(--wechat-text-secondary);
  position: relative;
  z-index: 1;
}

/* 输入框样式 */
.wechat-input {
  background: var(--wechat-surface);
  border: 1px solid var(--wechat-border);
  border-radius: var(--wechat-radius-xl);
  padding: var(--wechat-spacing-sm) var(--wechat-spacing-lg);
  font-size: var(--wechat-font-size-lg);
  line-height: var(--wechat-line-height-normal);
  transition: border-color var(--wechat-duration-fast);
}

.wechat-input:focus {
  border-color: var(--wechat-primary);
  outline: none;
}

/* 发送按钮 */
.wechat-send-button {
  background: var(--wechat-primary);
  color: white;
  border: none;
  border-radius: var(--wechat-radius-bubble);
  padding: var(--wechat-spacing-sm) var(--wechat-spacing-lg);
  font-size: var(--wechat-font-size-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--wechat-duration-fast);
  min-width: 60px;
  height: 36px;
}

.wechat-send-button:hover {
  background: var(--wechat-primary-dark);
  transform: translateY(-1px);
}

.wechat-send-button:active {
  transform: translateY(0);
}

.wechat-send-button:disabled {
  background: var(--wechat-text-tertiary);
  cursor: not-allowed;
  transform: none;
}

/* 头像样式 */
.wechat-avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--wechat-radius-sm);
  overflow: hidden;
  flex-shrink: 0;
  background: var(--wechat-border);
}

/* 工具按钮 */
.wechat-tool-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 1px solid var(--wechat-border);
  background: var(--wechat-surface);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--wechat-duration-fast);
}

.wechat-tool-button:hover {
  background: var(--wechat-border);
}

.wechat-tool-button.active {
  background: var(--wechat-primary);
  color: white;
  border-color: var(--wechat-primary);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .wechat-message-bubble {
    font-size: var(--wechat-font-size-md);
    padding: var(--wechat-spacing-sm) var(--wechat-spacing-md);
  }
  
  .wechat-avatar {
    width: 28px;
    height: 28px;
  }
  
  .wechat-tool-button {
    width: 32px;
    height: 32px;
  }
}

/* 现代审美升级 */

/* 高级毛玻璃效果 */
.wechat-glass-effect {
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.wechat-glass-effect-strong {
  backdrop-filter: blur(40px) saturate(200%) brightness(110%);
  -webkit-backdrop-filter: blur(40px) saturate(200%) brightness(110%);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow:
    0 16px 64px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.6),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
}

/* 输入区域毛玻璃效果 */
.input-area {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border-top: 1px solid rgba(229, 229, 229, 0.6);
}

/* 精致阴影层次系统 */
.wechat-shadow-subtle {
  box-shadow:
    0 0.8px 2px rgba(0, 0, 0, 0.08),
    0 1.6px 4px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

.wechat-shadow-soft {
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.06),
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.wechat-shadow-elevated {
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.08),
    0 8px 24px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.15);
}

.wechat-shadow-floating {
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.12),
    0 16px 48px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

/* 彩色阴影 */
.wechat-shadow-primary {
  box-shadow:
    0 4px 12px rgba(7, 193, 96, 0.15),
    0 8px 24px rgba(7, 193, 96, 0.08),
    0 0 0 1px rgba(7, 193, 96, 0.1);
}

.wechat-shadow-success {
  box-shadow:
    0 4px 12px rgba(76, 175, 80, 0.15),
    0 8px 24px rgba(76, 175, 80, 0.08);
}

.wechat-shadow-warning {
  box-shadow:
    0 4px 12px rgba(255, 152, 0, 0.15),
    0 8px 24px rgba(255, 152, 0, 0.08);
}

.wechat-shadow-danger {
  box-shadow:
    0 4px 12px rgba(244, 67, 54, 0.15),
    0 8px 24px rgba(244, 67, 54, 0.08);
}

/* 动态色彩系统 */
@media (prefers-color-scheme: light) {
  :root {
    --dynamic-primary: #07C160;
    --dynamic-surface: rgba(255, 255, 255, 0.95);
    --dynamic-overlay: rgba(0, 0, 0, 0.05);
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --dynamic-primary: #95EC69;
    --dynamic-surface: rgba(42, 42, 42, 0.95);
    --dynamic-overlay: rgba(255, 255, 255, 0.05);
  }
}

/* 时段色彩调整 */
@media (prefers-color-scheme: light) {
  /* 早晨 6-10点 - 清新绿 */
  .time-morning {
    --wechat-primary: #00D084;
    --wechat-primary-light: #7FEFB2;
  }

  /* 下午 14-18点 - 活力橙绿 */
  .time-afternoon {
    --wechat-primary: #07C160;
    --wechat-primary-light: #95EC69;
  }

  /* 晚上 19-23点 - 温暖绿 */
  .time-evening {
    --wechat-primary: #06AD56;
    --wechat-primary-light: #8FE063;
  }
}

/* 高级交互效果 */
.wechat-interactive {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
}

.wechat-interactive::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.wechat-interactive:hover::before {
  transform: translateX(100%);
}

.wechat-interactive:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 8px 24px rgba(7, 193, 96, 0.2),
    0 4px 8px rgba(7, 193, 96, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wechat-interactive:active {
  transform: translateY(-1px) scale(1.01);
  transition-duration: 0.1s;
}

/* 磁性吸附效果 */
.wechat-magnetic {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 呼吸效果 */
.wechat-breathing {
  animation: breathing 3s ease-in-out infinite;
}

@keyframes breathing {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* 脉冲效果 */
.wechat-pulse {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(7, 193, 96, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(7, 193, 96, 0);
  }
}

/* 波纹效果 */
.wechat-ripple {
  position: relative;
  overflow: hidden;
}

.wechat-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(7, 193, 96, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.wechat-ripple:active::after {
  width: 300px;
  height: 300px;
}

/* 高级消息气泡效果 */
.wechat-message-bubble {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.wechat-message-bubble::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  border-radius: inherit;
  filter: blur(20px);
  opacity: 0.3;
  z-index: -1;
}

.wechat-message-bubble--self {
  background: linear-gradient(135deg, #95EC69 0%, #7FE85C 50%, #6FD84F 100%);
  box-shadow:
    0 2px 8px rgba(149, 236, 105, 0.25),
    0 4px 16px rgba(149, 236, 105, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.wechat-message-bubble--self:hover {
  transform: translateY(-1px);
  box-shadow:
    0 4px 12px rgba(149, 236, 105, 0.3),
    0 8px 24px rgba(149, 236, 105, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.wechat-message-bubble--other {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8),
    0 0 0 1px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.wechat-message-bubble--other:hover {
  transform: translateY(-1px);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.12),
    0 8px 24px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* 高级输入框效果 */
.wechat-input {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.wechat-input::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(7, 193, 96, 0.05) 50%, transparent 70%);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.wechat-input:focus::before {
  opacity: 1;
}

.wechat-input:focus {
  border-color: rgba(7, 193, 96, 0.3);
  box-shadow:
    0 0 0 3px rgba(7, 193, 96, 0.1),
    0 4px 12px rgba(7, 193, 96, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

/* 高级发送按钮效果 */
.wechat-send-button {
  position: relative;
  background: linear-gradient(135deg, #07C160 0%, #06AD56 50%, #059649 100%);
  border: none;
  box-shadow:
    0 4px 8px rgba(7, 193, 96, 0.25),
    0 2px 4px rgba(7, 193, 96, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.wechat-send-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.wechat-send-button:hover::before {
  left: 100%;
}

.wechat-send-button:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    0 8px 16px rgba(7, 193, 96, 0.3),
    0 4px 8px rgba(7, 193, 96, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.wechat-send-button:active {
  transform: translateY(-1px) scale(1.02);
  transition-duration: 0.1s;
}

.wechat-send-button:disabled {
  background: linear-gradient(135deg, #CCCCCC 0%, #AAAAAA 100%);
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: none;
  cursor: not-allowed;
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  :root {
    --wechat-background: #1A1A1A;
    --wechat-surface: #2A2A2A;
    --wechat-text-primary: #FFFFFF;
    --wechat-text-secondary: #CCCCCC;
    --wechat-text-tertiary: #888888;
    --wechat-border: #333333;
    --wechat-divider: #404040;
  }

  .wechat-message-bubble--other {
    background: rgba(42, 42, 42, 0.95);
    border-color: var(--wechat-border);
    box-shadow:
      0 0.8px 2px rgba(255, 255, 255, 0.05),
      0 2px 4px rgba(255, 255, 255, 0.02);
  }

  .wechat-message-bubble--other::before {
    border-right-color: rgba(42, 42, 42, 0.95);
  }

  .input-area {
    background: rgba(26, 26, 26, 0.95);
    border-top-color: rgba(51, 51, 51, 0.6);
  }
}
