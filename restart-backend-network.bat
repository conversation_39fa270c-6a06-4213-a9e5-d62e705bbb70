@echo off
echo 正在重启后端服务以支持局域网访问...

echo 停止现有的后端进程...
taskkill /f /im dotnet.exe 2>nul

echo 等待进程完全停止...
timeout /t 3 /nobreak >nul

echo 启动后端服务（支持局域网访问）...
cd ../backend
start "Backend Server" cmd /k "dotnet run --launch-profile https"

echo 等待后端服务启动...
timeout /t 5 /nobreak >nul

echo 检查服务状态...
netstat -an | findstr 7250

echo.
echo 后端服务已重启！
echo HTTPS地址: https://192.168.110.226:7250
echo HTTP地址:  http://192.168.110.226:5057
echo.
echo 现在可以在真机上测试聊天功能了！
pause
