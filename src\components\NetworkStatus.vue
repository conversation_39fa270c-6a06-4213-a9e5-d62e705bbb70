<template>
  <div v-if="showStatus" class="network-status-container">
    <van-notice-bar
      :type="noticeType"
      :text="statusMessage"
      :closeable="true"
      @close="hideStatus"
    >
      <template #left-icon>
        <van-icon :name="statusIcon" />
      </template>
      <template #right-icon>
        <van-button 
          v-if="showRetryButton"
          size="mini" 
          type="primary" 
          @click="retryConnection"
          :loading="isRetrying"
        >
          重试
        </van-button>
      </template>
    </van-notice-bar>
    
    <!-- 详细诊断信息 -->
    <van-collapse v-if="showDetails" v-model="activeCollapse">
      <van-collapse-item title="网络诊断详情" name="1">
        <div class="diagnosis-content">
          <div v-if="diagnosis.issues.length > 0" class="issues-section">
            <h4>发现的问题：</h4>
            <ul>
              <li v-for="issue in diagnosis.issues" :key="issue">{{ issue }}</li>
            </ul>
          </div>
          
          <div v-if="diagnosis.suggestions.length > 0" class="suggestions-section">
            <h4>建议解决方案：</h4>
            <ul>
              <li v-for="suggestion in diagnosis.suggestions" :key="suggestion">{{ suggestion }}</li>
            </ul>
          </div>
          
          <div class="status-details">
            <p><strong>网络状态：</strong>{{ networkStatus.isOnline ? '在线' : '离线' }}</p>
            <p><strong>后端连接：</strong>{{ networkStatus.backendReachable ? '正常' : '异常' }}</p>
            <p><strong>延迟：</strong>{{ Math.round(networkStatus.latency) }}ms</p>
            <p><strong>最后检查：</strong>{{ formatTime(networkStatus.lastCheck) }}</p>
            <p v-if="networkStatus.error"><strong>错误信息：</strong>{{ networkStatus.error }}</p>
          </div>
        </div>
      </van-collapse-item>
    </van-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useNetworkStatus } from '@/utils/networkCheck'
import type { NetworkStatus } from '@/utils/networkCheck'

const showStatus = ref(false)
const showDetails = ref(false)
const isRetrying = ref(false)
const activeCollapse = ref<string[]>([])

const networkStatus = ref<NetworkStatus>({
  isOnline: true,
  backendReachable: true,
  latency: 0,
  lastCheck: new Date()
})

const diagnosis = ref<{
  issues: string[]
  suggestions: string[]
}>({
  issues: [],
  suggestions: []
})

const { getStatus, checkConnection, diagnose, addListener, removeListener } = useNetworkStatus()

// 计算状态类型
const noticeType = computed(() => {
  if (!networkStatus.value.isOnline) return 'danger'
  if (!networkStatus.value.backendReachable) return 'warning'
  if (networkStatus.value.latency > 1000) return 'primary'
  return 'success'
})

// 计算状态图标
const statusIcon = computed(() => {
  if (!networkStatus.value.isOnline) return 'warning-o'
  if (!networkStatus.value.backendReachable) return 'info-o'
  return 'success'
})

// 计算状态消息
const statusMessage = computed(() => {
  if (!networkStatus.value.isOnline) {
    return '网络连接已断开，请检查网络设置'
  }
  if (!networkStatus.value.backendReachable) {
    return '无法连接到服务器，部分功能可能不可用'
  }
  if (networkStatus.value.latency > 1000) {
    return `网络延迟较高 (${Math.round(networkStatus.value.latency)}ms)`
  }
  return '网络连接正常'
})

// 是否显示重试按钮
const showRetryButton = computed(() => {
  return !networkStatus.value.backendReachable
})

// 网络状态变化处理
const handleNetworkChange = async (status: NetworkStatus) => {
  networkStatus.value = status
  
  // 如果有网络问题，显示状态栏
  if (!status.isOnline || !status.backendReachable || status.latency > 1000) {
    showStatus.value = true
    
    // 获取诊断信息
    diagnosis.value = await diagnose()
    
    // 如果有严重问题，显示详细信息
    if (!status.isOnline || !status.backendReachable) {
      showDetails.value = true
    }
  } else {
    // 网络恢复正常，隐藏状态栏
    setTimeout(() => {
      showStatus.value = false
      showDetails.value = false
    }, 3000) // 3秒后自动隐藏
  }
}

// 重试连接
const retryConnection = async () => {
  isRetrying.value = true
  
  try {
    await checkConnection()
    // 状态会通过监听器自动更新
  } catch (error) {
    console.error('重试连接失败:', error)
  } finally {
    isRetrying.value = false
  }
}

// 隐藏状态栏
const hideStatus = () => {
  showStatus.value = false
  showDetails.value = false
}

// 格式化时间
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('zh-CN')
}

// 监听网络错误事件
const handleNetworkError = (event: CustomEvent) => {
  console.log('收到网络错误事件:', event.detail)
  showStatus.value = true
  
  // 更新状态以反映错误
  networkStatus.value = {
    ...networkStatus.value,
    backendReachable: false,
    error: event.detail.message
  }
}

onMounted(() => {
  // 获取初始状态
  networkStatus.value = getStatus()
  
  // 添加网络状态监听器
  addListener(handleNetworkChange)
  
  // 监听自定义网络错误事件
  window.addEventListener('network-error', handleNetworkError as EventListener)
  
  // 立即检查一次连接
  checkConnection()
})

onUnmounted(() => {
  removeListener(handleNetworkChange)
  window.removeEventListener('network-error', handleNetworkError as EventListener)
})
</script>

<style scoped>
.network-status-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: var(--wechat-surface);
}

.diagnosis-content {
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
}

.issues-section,
.suggestions-section {
  margin-bottom: 16px;
}

.issues-section h4 {
  color: #F56C6C;
  margin: 0 0 8px 0;
  font-size: 14px;
}

.suggestions-section h4 {
  color: var(--wechat-primary);
  margin: 0 0 8px 0;
  font-size: 14px;
}

.issues-section ul,
.suggestions-section ul {
  margin: 0;
  padding-left: 20px;
}

.issues-section li {
  color: #F56C6C;
  margin-bottom: 4px;
}

.suggestions-section li {
  color: var(--wechat-text-primary);
  margin-bottom: 4px;
}

.status-details {
  background: var(--wechat-background);
  padding: 12px;
  border-radius: 8px;
  margin-top: 12px;
}

.status-details p {
  margin: 4px 0;
  font-size: 13px;
  color: var(--wechat-text-secondary);
}

.status-details strong {
  color: var(--wechat-text-primary);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .diagnosis-content {
    padding: 8px;
    font-size: 13px;
  }
  
  .status-details {
    padding: 8px;
  }
  
  .status-details p {
    font-size: 12px;
  }
}
</style>
