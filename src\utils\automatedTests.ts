/**
 * 自动化测试运行器
 * 在应用启动时自动运行基础测试，确保核心功能正常
 */

import { TestRunner, MessageBubbleTests, InteractionTests, PerformanceTests } from './testUtils'

export interface AutoTestConfig {
  enableOnStartup: boolean
  enablePerformanceMonitoring: boolean
  performanceThresholds: {
    renderTime: number // ms
    scrollPerformance: number // ms
    memoryUsage: number // MB
    messageLoadTime: number // ms
    interactionLatency: number // ms
  }
}

export class AutomatedTestRunner {
  private static instance: AutomatedTestRunner
  private testRunner: TestRunner
  private config: AutoTestConfig
  private performanceMonitor: PerformanceMonitor | null = null
  
  private constructor() {
    this.testRunner = new TestRunner()
    this.config = {
      enableOnStartup: true,
      enablePerformanceMonitoring: true,
      performanceThresholds: {
        renderTime: 100, // 100ms
        scrollPerformance: 50, // 50ms
        memoryUsage: 50, // 50MB
        messageLoadTime: 200, // 200ms
        interactionLatency: 16 // 16ms (60fps)
      }
    }
  }
  
  static getInstance(): AutomatedTestRunner {
    if (!AutomatedTestRunner.instance) {
      AutomatedTestRunner.instance = new AutomatedTestRunner()
    }
    return AutomatedTestRunner.instance
  }
  
  /**
   * 应用启动时运行基础测试
   */
  async runStartupTests(): Promise<boolean> {
    if (!this.config.enableOnStartup) return true
    
    console.log('🧪 运行启动测试...')
    
    try {
      // 运行关键功能测试
      const criticalTests = [
        { name: '消息气泡渲染', fn: MessageBubbleTests.testMessageBubbleRendering },
        { name: '基础交互', fn: InteractionTests.testMessageSending }
      ]
      
      const results = await this.testRunner.runTestSuite(criticalTests)
      const failedTests = results.filter(r => !r.passed)
      
      if (failedTests.length > 0) {
        console.error('❌ 启动测试失败:', failedTests)
        return false
      }
      
      console.log('✅ 启动测试通过')
      
      // 启动性能监控
      if (this.config.enablePerformanceMonitoring) {
        this.startPerformanceMonitoring()
      }
      
      return true
      
    } catch (error) {
      console.error('❌ 启动测试异常:', error)
      return false
    }
  }
  
  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring() {
    this.performanceMonitor = new PerformanceMonitor(this.config.performanceThresholds)
    this.performanceMonitor.start()
  }
  
  /**
   * 运行完整测试套件
   */
  async runFullTestSuite(): Promise<{ passed: boolean, results: any[], metrics: any }> {
    console.log('🧪 运行完整测试套件...')
    
    const allTests = [
      { name: '消息气泡渲染测试', fn: MessageBubbleTests.testMessageBubbleRendering },
      { name: '长消息处理测试', fn: MessageBubbleTests.testLongMessageHandling },
      { name: '引用回复渲染测试', fn: MessageBubbleTests.testQuoteReplyRendering },
      { name: '消息发送交互测试', fn: InteractionTests.testMessageSending },
      { name: '键盘交互测试', fn: InteractionTests.testKeyboardInteraction }
    ]
    
    const results = await this.testRunner.runTestSuite(allTests)
    
    // 运行性能测试
    const renderTime = await PerformanceTests.testMessageLoadTime(100)
    const scrollPerformance = await InteractionTests.testScrollPerformance()
    const memoryUsage = PerformanceTests.getMemoryUsage()
    const messageLoadTime = await PerformanceTests.testMessageLoadTime(50)
    const interactionLatency = await PerformanceTests.testInteractionLatency()
    
    const metrics = {
      renderTime,
      scrollPerformance,
      memoryUsage,
      messageLoadTime,
      interactionLatency
    }
    
    const summary = this.testRunner.getTestSummary()
    const passed = summary.failed === 0
    
    console.log(passed ? '✅ 完整测试套件通过' : '❌ 完整测试套件失败')
    
    return { passed, results, metrics }
  }
  
  /**
   * 检查性能是否符合阈值
   */
  checkPerformanceThresholds(metrics: any): { passed: boolean, violations: string[] } {
    const violations: string[] = []
    const thresholds = this.config.performanceThresholds
    
    if (metrics.renderTime > thresholds.renderTime) {
      violations.push(`渲染时间超标: ${metrics.renderTime}ms > ${thresholds.renderTime}ms`)
    }
    
    if (metrics.scrollPerformance > thresholds.scrollPerformance) {
      violations.push(`滚动性能超标: ${metrics.scrollPerformance}ms > ${thresholds.scrollPerformance}ms`)
    }
    
    if (metrics.memoryUsage > thresholds.memoryUsage) {
      violations.push(`内存使用超标: ${metrics.memoryUsage}MB > ${thresholds.memoryUsage}MB`)
    }
    
    if (metrics.messageLoadTime > thresholds.messageLoadTime) {
      violations.push(`消息加载超标: ${metrics.messageLoadTime}ms > ${thresholds.messageLoadTime}ms`)
    }
    
    if (metrics.interactionLatency > thresholds.interactionLatency) {
      violations.push(`交互延迟超标: ${metrics.interactionLatency}ms > ${thresholds.interactionLatency}ms`)
    }
    
    return { passed: violations.length === 0, violations }
  }
  
  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<AutoTestConfig>) {
    this.config = { ...this.config, ...newConfig }
  }
  
  /**
   * 获取测试结果
   */
  getTestResults() {
    return this.testRunner.getAllResults()
  }
  
  /**
   * 获取性能指标
   */
  getPerformanceMetrics() {
    return this.testRunner.getPerformanceMetrics()
  }
  
  /**
   * 停止监控
   */
  stop() {
    if (this.performanceMonitor) {
      this.performanceMonitor.stop()
      this.performanceMonitor = null
    }
  }
}

/**
 * 性能监控器
 */
class PerformanceMonitor {
  private thresholds: AutoTestConfig['performanceThresholds']
  private isRunning = false
  private intervalId: number | null = null
  
  constructor(thresholds: AutoTestConfig['performanceThresholds']) {
    this.thresholds = thresholds
  }
  
  start() {
    if (this.isRunning) return
    
    this.isRunning = true
    console.log('📊 启动性能监控')
    
    // 每30秒检查一次性能
    this.intervalId = window.setInterval(() => {
      this.checkPerformance()
    }, 30000)
  }
  
  stop() {
    if (!this.isRunning) return
    
    this.isRunning = false
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
    
    console.log('📊 停止性能监控')
  }
  
  private async checkPerformance() {
    try {
      const memoryUsage = PerformanceTests.getMemoryUsage()
      const interactionLatency = await PerformanceTests.testInteractionLatency()
      
      // 检查内存使用
      if (memoryUsage > this.thresholds.memoryUsage) {
        console.warn(`⚠️ 内存使用过高: ${memoryUsage}MB`)
      }
      
      // 检查交互延迟
      if (interactionLatency > this.thresholds.interactionLatency) {
        console.warn(`⚠️ 交互延迟过高: ${interactionLatency}ms`)
      }
      
    } catch (error) {
      console.error('性能监控检查失败:', error)
    }
  }
}

/**
 * 测试报告生成器
 */
export class TestReportGenerator {
  static generateReport(results: any[], metrics: any): string {
    const timestamp = new Date().toLocaleString('zh-CN')
    const summary = {
      total: results.length,
      passed: results.filter(r => r.passed).length,
      failed: results.filter(r => !r.passed).length,
      duration: results.reduce((sum, r) => sum + r.duration, 0)
    }
    
    let report = `# 聊天应用测试报告\n\n`
    report += `**生成时间**: ${timestamp}\n\n`
    
    report += `## 测试摘要\n\n`
    report += `- 总计: ${summary.total}\n`
    report += `- 通过: ${summary.passed}\n`
    report += `- 失败: ${summary.failed}\n`
    report += `- 总耗时: ${Math.round(summary.duration)}ms\n\n`
    
    report += `## 性能指标\n\n`
    report += `- 渲染时间: ${Math.round(metrics.renderTime)}ms\n`
    report += `- 滚动性能: ${Math.round(metrics.scrollPerformance)}ms\n`
    report += `- 内存使用: ${Math.round(metrics.memoryUsage)}MB\n`
    report += `- 消息加载: ${Math.round(metrics.messageLoadTime)}ms\n`
    report += `- 交互延迟: ${Math.round(metrics.interactionLatency)}ms\n\n`
    
    report += `## 详细结果\n\n`
    results.forEach(result => {
      const status = result.passed ? '✅' : '❌'
      report += `${status} **${result.name}** (${Math.round(result.duration)}ms)\n`
      if (result.error) {
        report += `   错误: ${result.error}\n`
      }
      report += `\n`
    })
    
    return report
  }
  
  static downloadReport(content: string, filename = 'test-report.md') {
    const blob = new Blob([content], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)
  }
}
