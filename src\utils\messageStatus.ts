/**
 * 消息状态管理工具
 * 处理消息发送状态、已读状态、时间戳显示等
 */

export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read' | 'failed'

export interface MessageWithStatus {
  id: string | number
  content: string
  senderId: number
  senderName: string
  createdAt: string
  status: MessageStatus
  isRead?: boolean
  readAt?: string
  type: 'text' | 'image' | 'voice' | 'file' | 'quote'
  quotedMessage?: MessageWithStatus
}

export class MessageStatusManager {
  private statusCallbacks: Map<string | number, (status: MessageStatus) => void> = new Map()
  private readCallbacks: Map<string | number, (readAt: string) => void> = new Map()
  
  /**
   * 注册消息状态变化回调
   */
  onStatusChange(messageId: string | number, callback: (status: MessageStatus) => void) {
    this.statusCallbacks.set(messageId, callback)
  }
  
  /**
   * 注册消息已读回调
   */
  onRead(messageId: string | number, callback: (readAt: string) => void) {
    this.readCallbacks.set(messageId, callback)
  }
  
  /**
   * 更新消息状态
   */
  updateStatus(messageId: string | number, status: MessageStatus) {
    const callback = this.statusCallbacks.get(messageId)
    if (callback) {
      callback(status)
    }
  }
  
  /**
   * 标记消息为已读
   */
  markAsRead(messageId: string | number, readAt: string = new Date().toISOString()) {
    const callback = this.readCallbacks.get(messageId)
    if (callback) {
      callback(readAt)
    }
  }
  
  /**
   * 清理回调
   */
  cleanup(messageId: string | number) {
    this.statusCallbacks.delete(messageId)
    this.readCallbacks.delete(messageId)
  }
  
  /**
   * 清理所有回调
   */
  cleanupAll() {
    this.statusCallbacks.clear()
    this.readCallbacks.clear()
  }
}

/**
 * 时间戳智能显示工具
 */
export class TimestampManager {
  /**
   * 判断是否应该显示时间戳
   * 规则：2分钟内不重复显示，不同发送者总是显示
   */
  static shouldShowTimestamp(
    currentMessage: MessageWithStatus,
    previousMessage?: MessageWithStatus
  ): boolean {
    if (!previousMessage) return true
    
    // 不同发送者，显示时间戳
    if (currentMessage.senderId !== previousMessage.senderId) {
      return true
    }
    
    // 相同发送者，检查时间间隔
    const currentTime = new Date(currentMessage.createdAt).getTime()
    const previousTime = new Date(previousMessage.createdAt).getTime()
    const timeDiff = currentTime - previousTime
    
    // 超过2分钟，显示时间戳
    return timeDiff > 2 * 60 * 1000
  }
  
  /**
   * 格式化消息时间显示
   */
  static formatMessageTime(timestamp: string): string {
    const messageTime = new Date(timestamp)
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const messageDate = new Date(messageTime.getFullYear(), messageTime.getMonth(), messageTime.getDate())
    
    // 今天的消息只显示时间
    if (messageDate.getTime() === today.getTime()) {
      return messageTime.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      })
    }
    
    // 昨天的消息
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    if (messageDate.getTime() === yesterday.getTime()) {
      return `昨天 ${messageTime.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      })}`
    }
    
    // 本周内的消息显示星期
    const weekStart = new Date(today)
    weekStart.setDate(today.getDate() - today.getDay())
    if (messageDate >= weekStart) {
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      return `${weekdays[messageTime.getDay()]} ${messageTime.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      })}`
    }
    
    // 更早的消息显示完整日期
    return messageTime.toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  }
  
  /**
   * 格式化日期分隔线显示
   */
  static formatDateDivider(timestamp: string): string {
    const messageDate = new Date(timestamp)
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const messageDateOnly = new Date(messageDate.getFullYear(), messageDate.getMonth(), messageDate.getDate())
    
    // 今天
    if (messageDateOnly.getTime() === today.getTime()) {
      return '今天'
    }
    
    // 昨天
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    if (messageDateOnly.getTime() === yesterday.getTime()) {
      return '昨天'
    }
    
    // 本周内
    const weekStart = new Date(today)
    weekStart.setDate(today.getDate() - today.getDay())
    if (messageDateOnly >= weekStart) {
      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      return weekdays[messageDate.getDay()]
    }
    
    // 本年内
    if (messageDate.getFullYear() === now.getFullYear()) {
      return messageDate.toLocaleDateString('zh-CN', {
        month: 'long',
        day: 'numeric'
      })
    }
    
    // 跨年
    return messageDate.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }
}

/**
 * 连续消息显示优化工具
 */
export class ContinuousMessageManager {
  /**
   * 判断是否应该显示为连续消息
   * 规则：相同发送者且时间间隔小于5分钟
   */
  static shouldShowAsContinuous(
    currentMessage: MessageWithStatus,
    previousMessage?: MessageWithStatus
  ): boolean {
    if (!previousMessage) return false
    
    // 不同发送者，不连续
    if (currentMessage.senderId !== previousMessage.senderId) {
      return false
    }
    
    // 检查时间间隔
    const currentTime = new Date(currentMessage.createdAt).getTime()
    const previousTime = new Date(previousMessage.createdAt).getTime()
    const timeDiff = currentTime - previousTime
    
    // 小于5分钟，显示为连续
    return timeDiff < 5 * 60 * 1000
  }
  
  /**
   * 获取连续消息组
   */
  static groupContinuousMessages(messages: MessageWithStatus[]): MessageWithStatus[][] {
    if (messages.length === 0) return []
    
    const groups: MessageWithStatus[][] = []
    let currentGroup: MessageWithStatus[] = [messages[0]]
    
    for (let i = 1; i < messages.length; i++) {
      const currentMessage = messages[i]
      const previousMessage = messages[i - 1]
      
      if (this.shouldShowAsContinuous(currentMessage, previousMessage)) {
        currentGroup.push(currentMessage)
      } else {
        groups.push(currentGroup)
        currentGroup = [currentMessage]
      }
    }
    
    groups.push(currentGroup)
    return groups
  }
}

/**
 * 引用回复工具
 */
export class QuoteReplyManager {
  /**
   * 创建引用消息
   */
  static createQuoteMessage(
    originalMessage: MessageWithStatus,
    replyContent: string,
    senderId: number,
    senderName: string
  ): MessageWithStatus {
    return {
      id: Date.now(), // 临时ID，实际应该由后端生成
      content: replyContent,
      senderId,
      senderName,
      createdAt: new Date().toISOString(),
      status: 'sending',
      type: 'quote',
      quotedMessage: {
        ...originalMessage,
        // 截取引用内容，避免过长
        content: originalMessage.content.length > 100 
          ? originalMessage.content.substring(0, 100) + '...'
          : originalMessage.content
      }
    }
  }
  
  /**
   * 格式化引用消息显示
   */
  static formatQuoteContent(quotedMessage: MessageWithStatus): string {
    const maxLength = 50
    if (quotedMessage.content.length <= maxLength) {
      return quotedMessage.content
    }
    return quotedMessage.content.substring(0, maxLength) + '...'
  }
}
