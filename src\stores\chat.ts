import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import signalRService from '@/services/signalr'
import { messageAPI } from '@/services/api'

export interface Message {
  id: number
  senderId: number
  senderName: string
  content: string
  createdAt: string
  type: 'private' | 'group'
  receiverId?: number
  groupId?: number
  isRead?: boolean
}

export interface ChatSession {
  id: string // 对于私聊是用户ID，对于群聊是群组ID
  type: 'private' | 'group'
  name: string
  avatar?: string
  lastMessage?: Message
  unreadCount: number
  isOnline?: boolean // 仅对私聊有效
}

export const useChatStore = defineStore('chat', () => {
  // 状态
  const messages = ref<Record<string, Message[]>>({}) // 按会话ID分组的消息
  const chatSessions = ref<ChatSession[]>([])
  const currentChatId = ref<string | null>(null)
  const isLoading = ref(false)

  // 计算属性
  const currentMessages = computed(() => {
    if (!currentChatId.value) return []
    return messages.value[currentChatId.value] || []
  })

  const currentChat = computed(() => {
    if (!currentChatId.value) return null
    return chatSessions.value.find(chat => chat.id === currentChatId.value) || null
  })

  const totalUnreadCount = computed(() => {
    return chatSessions.value.reduce((total, chat) => total + chat.unreadCount, 0)
  })

  // 初始化SignalR事件监听
  const initializeSignalR = () => {
    // 监听接收消息
    signalRService.onReceiveMessage((messageData: any) => {
      console.log('收到新消息:', messageData)

      // 转换消息格式
      const message: Message = {
        id: messageData.id,
        senderId: messageData.senderId,
        senderName: messageData.sender?.displayName || messageData.sender?.username || '',
        content: messageData.content,
        createdAt: messageData.createdAt,
        type: messageData.type,
        receiverId: messageData.receiverId,
        groupId: messageData.groupId,
        isRead: messageData.isRead,
        sender: messageData.sender
      }

      addMessage(message)
    })

    // 监听消息发送确认
    signalRService.onMessageSent((messageData: any) => {
      console.log('消息发送确认:', messageData)

      // 转换消息格式
      const message: Message = {
        id: messageData.id,
        senderId: messageData.senderId,
        senderName: messageData.sender?.displayName || messageData.sender?.username || '',
        content: messageData.content,
        createdAt: messageData.createdAt,
        type: messageData.type,
        receiverId: messageData.receiverId,
        groupId: messageData.groupId,
        isRead: messageData.isRead,
        sender: messageData.sender
      }

      addMessage(message)
    })

    // 监听好友状态变化
    signalRService.onFriendStatusChanged((data: { userId: number; isOnline: boolean }) => {
      updateFriendStatus(data.userId, data.isOnline)
    })

    // 监听错误
    signalRService.onError((error: string) => {
      console.error('SignalR错误:', error)
    })
  }

  // 添加消息
  const addMessage = (message: Message) => {
    // 获取当前用户ID
    const { useAuthStore } = require('@/stores/auth')
    const authStore = useAuthStore()
    const currentUserId = authStore.user?.id

    // 确定聊天ID
    let chatId: string
    if (message.type === 'private') {
      // 私聊：使用对方的ID作为chatId
      if (message.senderId === currentUserId) {
        chatId = message.receiverId?.toString() || ''
      } else {
        chatId = message.senderId.toString()
      }
    } else {
      // 群聊：使用群组ID
      chatId = message.groupId?.toString() || ''
    }

    if (!chatId) return

    if (!messages.value[chatId]) {
      messages.value[chatId] = []
    }

    // 检查消息是否已存在（避免重复）
    const existingMessage = messages.value[chatId].find(m => m.id === message.id)
    if (!existingMessage) {
      messages.value[chatId].push(message)

      // 按时间排序
      messages.value[chatId].sort((a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      )

      // 更新会话信息
      updateChatSession(chatId, message)
    }
  }

  // 更新会话信息（微信风格）
  const updateChatSession = (chatId: string, message: Message) => {
    console.log('更新会话信息:', { chatId, message })

    let existingSession = chatSessions.value.find(chat => chat.id === chatId)

    if (existingSession) {
      // 更新现有会话
      existingSession.lastMessage = message

      // 只有不是当前聊天窗口才增加未读数
      if (chatId !== currentChatId.value) {
        existingSession.unreadCount = (existingSession.unreadCount || 0) + 1
      }

      console.log('更新现有会话:', existingSession)
    } else {
      // 创建新的会话（微信风格：有消息就自动创建会话）
      const newSession: ChatSession = {
        id: chatId,
        type: message.type,
        name: getSessionName(message),
        avatar: getSessionAvatar(message),
        lastMessage: message,
        unreadCount: chatId !== currentChatId.value ? 1 : 0,
        isOnline: message.type === 'private' ? true : undefined
      }

      chatSessions.value.push(newSession)
      console.log('创建新会话:', newSession)
    }

    // 按最后消息时间排序（最新的在最上面）
    chatSessions.value.sort((a, b) => {
      const aTime = a.lastMessage ? new Date(a.lastMessage.createdAt).getTime() : 0
      const bTime = b.lastMessage ? new Date(b.lastMessage.createdAt).getTime() : 0
      return bTime - aTime
    })

    // 持久化保存会话列表
    saveChatSessions()
  }

  // 获取会话名称
  const getSessionName = (message: Message): string => {
    if (message.type === 'private') {
      return message.senderName || `用户${message.senderId}`
    } else {
      return `群聊${message.groupId}`
    }
  }

  // 获取会话头像
  const getSessionAvatar = (message: Message): string => {
    // 这里可以根据实际需求获取用户或群组头像
    return 'https://img.yzcdn.cn/vant/cat.jpeg'
  }

  // 保存会话列表到本地存储
  const saveChatSessions = () => {
    try {
      localStorage.setItem('chatSessions', JSON.stringify(chatSessions.value))
    } catch (error) {
      console.error('保存会话列表失败:', error)
    }
  }

  // 从本地存储加载会话列表
  const loadChatSessions = () => {
    try {
      const saved = localStorage.getItem('chatSessions')
      if (saved) {
        chatSessions.value = JSON.parse(saved)
        console.log('加载会话列表:', chatSessions.value)
      }
    } catch (error) {
      console.error('加载会话列表失败:', error)
    }
  }

  // 更新好友在线状态
  const updateFriendStatus = (userId: number, isOnline: boolean) => {
    const session = chatSessions.value.find(chat => 
      chat.type === 'private' && chat.id === userId.toString()
    )
    if (session) {
      session.isOnline = isOnline
    }
  }

  // 设置当前聊天
  const setCurrentChat = (chatId: string) => {
    currentChatId.value = chatId
    
    // 清除未读计数
    const session = chatSessions.value.find(chat => chat.id === chatId)
    if (session) {
      session.unreadCount = 0
    }
  }

  // 发送私聊消息
  const sendPrivateMessage = async (receiverId: number, content: string) => {
    console.log('开始发送私聊消息:', { receiverId, content })
    console.log('SignalR连接状态:', signalRService.isConnected)

    try {
      await signalRService.sendPrivateMessage(receiverId, content)
      console.log('私聊消息发送成功')
    } catch (error) {
      console.error('发送私聊消息失败:', error)
      throw error
    }
  }

  // 发送群聊消息
  const sendGroupMessage = async (groupId: number, content: string) => {
    try {
      await signalRService.sendGroupMessage(groupId, content)
    } catch (error) {
      console.error('发送群聊消息失败:', error)
      throw error
    }
  }

  // 添加聊天会话
  const addChatSession = (session: ChatSession) => {
    const existingSession = chatSessions.value.find(chat => chat.id === session.id)
    if (!existingSession) {
      chatSessions.value.push(session)
    }
  }

  // 加载历史消息
  const loadHistoryMessages = async (chatId: string, type: 'private' | 'group') => {
    try {
      console.log(`加载${type}聊天历史消息，chatId: ${chatId}`)

      let response
      if (type === 'private') {
        response = await messageAPI.getPrivateMessages(parseInt(chatId))
      } else {
        response = await messageAPI.getGroupMessages(parseInt(chatId))
      }

      console.log('API响应:', response)

      // API拦截器已经返回了response.data，所以直接使用response
      if (response && Array.isArray(response) && response.length > 0) {
        if (!messages.value[chatId]) {
          messages.value[chatId] = []
        }

        // 转换消息格式以匹配前端接口
        const formattedMessages = response.map((msg: any) => ({
          id: msg.id,
          senderId: msg.senderId,
          senderName: msg.sender?.displayName || msg.sender?.username || '',
          content: msg.content,
          createdAt: msg.createdAt,
          type: msg.type,
          receiverId: msg.receiverId,
          groupId: msg.groupId,
          isRead: msg.isRead,
          sender: msg.sender
        }))

        // 合并历史消息，避免重复
        const existingIds = new Set(messages.value[chatId].map(m => m.id))
        const newMessages = formattedMessages.filter((m: Message) => !existingIds.has(m.id))

        messages.value[chatId] = [...newMessages, ...messages.value[chatId]]

        // 按时间排序
        messages.value[chatId].sort((a, b) =>
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        )

        console.log(`成功加载${newMessages.length}条历史消息`)
      } else {
        console.log('没有历史消息或响应格式错误')
      }
    } catch (error) {
      console.error('加载历史消息失败:', error)
    }
  }

  // 标记消息为已读
  const markAsRead = async (chatId: string, type: 'private' | 'group') => {
    try {
      if (type === 'private') {
        await messageAPI.markAsRead({ type: 'private', senderId: parseInt(chatId) })
      } else {
        await messageAPI.markAsRead({ type: 'group', groupId: parseInt(chatId) })
      }

      // 更新本地未读状态
      const session = chatSessions.value.find(s => s.id === chatId)
      if (session) {
        session.unreadCount = 0
      }
    } catch (error) {
      console.error('标记已读失败:', error)
    }
  }

  // 删除会话（微信风格：长按删除）
  const deleteChatSession = (chatId: string) => {
    const index = chatSessions.value.findIndex(session => session.id === chatId)
    if (index > -1) {
      chatSessions.value.splice(index, 1)
      // 同时删除消息记录
      delete messages.value[chatId]
      // 保存到本地存储
      saveChatSessions()
      console.log('删除会话:', chatId)
    }
  }

  // 清空会话的未读数（进入聊天时调用）
  const clearUnreadCount = (chatId: string) => {
    const session = chatSessions.value.find(s => s.id === chatId)
    if (session) {
      session.unreadCount = 0
      saveChatSessions()
    }
  }

  // 初始化聊天store
  const initializeChatStore = () => {
    loadChatSessions()
    initializeSignalR()
  }

  // 清除所有数据
  const clearAll = () => {
    messages.value = {}
    chatSessions.value = []
    currentChatId.value = null
    localStorage.removeItem('chatSessions')
  }

  return {
    // 状态
    messages,
    chatSessions,
    currentChatId,
    isLoading,
    
    // 计算属性
    currentMessages,
    currentChat,
    totalUnreadCount,
    
    // 方法
    initializeSignalR,
    initializeChatStore,
    addMessage,
    updateChatSession,
    updateFriendStatus,
    setCurrentChat,
    sendPrivateMessage,
    sendGroupMessage,
    addChatSession,
    loadHistoryMessages,
    markAsRead,
    deleteChatSession,
    clearUnreadCount,
    saveChatSessions,
    loadChatSessions,
    clearAll
  }
})
