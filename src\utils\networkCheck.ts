/**
 * 网络状态检查工具
 * 用于检测前后端连接状态和诊断网络问题
 */

export interface NetworkStatus {
  isOnline: boolean
  backendReachable: boolean
  latency: number
  lastCheck: Date
  error?: string
}

export class NetworkChecker {
  private static instance: NetworkChecker
  private status: NetworkStatus = {
    isOnline: navigator.onLine,
    backendReachable: false,
    latency: 0,
    lastCheck: new Date()
  }
  
  private listeners: Array<(status: NetworkStatus) => void> = []
  private checkInterval: number | null = null
  
  static getInstance(): NetworkChecker {
    if (!NetworkChecker.instance) {
      NetworkChecker.instance = new NetworkChecker()
    }
    return NetworkChecker.instance
  }
  
  private constructor() {
    this.setupEventListeners()
    this.startPeriodicCheck()
  }
  
  /**
   * 设置网络状态监听器
   */
  private setupEventListeners() {
    window.addEventListener('online', () => {
      this.status.isOnline = true
      this.checkBackendConnection()
    })
    
    window.addEventListener('offline', () => {
      this.status.isOnline = false
      this.status.backendReachable = false
      this.notifyListeners()
    })
  }
  
  /**
   * 开始定期检查
   */
  private startPeriodicCheck() {
    // 每30秒检查一次后端连接
    this.checkInterval = window.setInterval(() => {
      if (this.status.isOnline) {
        this.checkBackendConnection()
      }
    }, 30000)
  }
  
  /**
   * 检查后端连接状态
   */
  async checkBackendConnection(): Promise<boolean> {
    try {
      const startTime = performance.now()
      
      // 尝试访问后端健康检查端点
      const response = await fetch('/api/health', {
        method: 'GET',
        cache: 'no-cache',
        signal: AbortSignal.timeout(5000) // 5秒超时
      })
      
      const endTime = performance.now()
      const latency = endTime - startTime
      
      const isReachable = response.ok
      
      this.status = {
        isOnline: navigator.onLine,
        backendReachable: isReachable,
        latency,
        lastCheck: new Date(),
        error: isReachable ? undefined : `HTTP ${response.status}: ${response.statusText}`
      }
      
      this.notifyListeners()
      return isReachable
      
    } catch (error) {
      this.status = {
        isOnline: navigator.onLine,
        backendReachable: false,
        latency: 0,
        lastCheck: new Date(),
        error: error instanceof Error ? error.message : '网络连接失败'
      }
      
      this.notifyListeners()
      return false
    }
  }
  
  /**
   * 诊断网络问题
   */
  async diagnoseNetworkIssues(): Promise<{
    issues: string[]
    suggestions: string[]
  }> {
    const issues: string[] = []
    const suggestions: string[] = []
    
    // 检查基本网络连接
    if (!this.status.isOnline) {
      issues.push('设备离线')
      suggestions.push('请检查网络连接')
    }
    
    // 检查后端连接
    if (this.status.isOnline && !this.status.backendReachable) {
      issues.push('无法连接到后端服务器')
      suggestions.push('请确认后端服务器是否正在运行')
      suggestions.push('检查后端服务器地址配置是否正确')
      
      if (this.status.error?.includes('CORS')) {
        issues.push('跨域请求被阻止')
        suggestions.push('请检查后端CORS配置')
        suggestions.push('确认前端代理配置是否正确')
      }
      
      if (this.status.error?.includes('timeout')) {
        issues.push('请求超时')
        suggestions.push('检查网络延迟')
        suggestions.push('确认后端服务器响应速度')
      }
    }
    
    // 检查延迟
    if (this.status.latency > 1000) {
      issues.push('网络延迟过高')
      suggestions.push('检查网络质量')
      suggestions.push('尝试重启网络连接')
    }
    
    return { issues, suggestions }
  }
  
  /**
   * 获取当前网络状态
   */
  getStatus(): NetworkStatus {
    return { ...this.status }
  }
  
  /**
   * 添加状态变化监听器
   */
  addListener(callback: (status: NetworkStatus) => void) {
    this.listeners.push(callback)
  }
  
  /**
   * 移除状态变化监听器
   */
  removeListener(callback: (status: NetworkStatus) => void) {
    const index = this.listeners.indexOf(callback)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }
  
  /**
   * 通知所有监听器
   */
  private notifyListeners() {
    this.listeners.forEach(callback => {
      try {
        callback(this.status)
      } catch (error) {
        console.error('网络状态监听器错误:', error)
      }
    })
  }
  
  /**
   * 停止网络检查
   */
  stop() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }
    
    window.removeEventListener('online', this.setupEventListeners)
    window.removeEventListener('offline', this.setupEventListeners)
  }
}

/**
 * 网络重试工具
 */
export class NetworkRetry {
  /**
   * 带重试的网络请求
   */
  static async withRetry<T>(
    requestFn: () => Promise<T>,
    options: {
      maxRetries?: number
      delay?: number
      backoff?: boolean
    } = {}
  ): Promise<T> {
    const { maxRetries = 3, delay = 1000, backoff = true } = options
    
    let lastError: Error
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await requestFn()
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error))
        
        if (attempt === maxRetries) {
          throw lastError
        }
        
        // 计算延迟时间
        const waitTime = backoff ? delay * Math.pow(2, attempt) : delay
        
        console.warn(`请求失败，${waitTime}ms后重试 (${attempt + 1}/${maxRetries + 1}):`, lastError.message)
        
        await new Promise(resolve => setTimeout(resolve, waitTime))
      }
    }
    
    throw lastError!
  }
}

/**
 * 网络状态显示组件数据
 */
export function useNetworkStatus() {
  const checker = NetworkChecker.getInstance()
  
  return {
    getStatus: () => checker.getStatus(),
    checkConnection: () => checker.checkBackendConnection(),
    diagnose: () => checker.diagnoseNetworkIssues(),
    addListener: (callback: (status: NetworkStatus) => void) => checker.addListener(callback),
    removeListener: (callback: (status: NetworkStatus) => void) => checker.removeListener(callback)
  }
}
