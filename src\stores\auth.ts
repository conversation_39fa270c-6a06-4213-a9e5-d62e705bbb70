import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI } from '@/services/api'
import signalRService from '@/services/signalr'

export interface User {
  id: number
  username: string
  email: string
  displayName: string
  avatar?: string
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)

  // 初始化 - 从localStorage恢复状态
  const initialize = async () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')

    if (savedToken && savedUser) {
      token.value = savedToken
      user.value = JSON.parse(savedUser)

      // 初始化SignalR连接
      try {
        await signalRService.initialize(savedToken)
        // 初始化聊天store的SignalR监听
        const { useChatStore } = await import('@/stores/chat')
        const chatStore = useChatStore()
        chatStore.initializeSignalR()
      } catch (error) {
        console.error('初始化SignalR失败:', error)
      }
    }
  }

  // 登录
  const login = async (username: string, password: string) => {
    isLoading.value = true
    try {
      const response = await authAPI.login(username, password)
      
      // 保存用户信息和token
      user.value = response.user
      token.value = response.token
      
      // 保存到localStorage
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      
      // 初始化SignalR连接
      await signalRService.initialize(response.token)

      // 初始化聊天store的SignalR监听
      const { useChatStore } = await import('@/stores/chat')
      const chatStore = useChatStore()
      chatStore.initializeSignalR()

      return response
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (username: string, email: string, password: string, displayName?: string) => {
    isLoading.value = true
    try {
      const response = await authAPI.register(username, email, password, displayName)
      
      // 保存用户信息和token
      user.value = response.user
      token.value = response.token
      
      // 保存到localStorage
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      
      // 初始化SignalR连接
      await signalRService.initialize(response.token)

      // 初始化聊天store的SignalR监听
      const { useChatStore } = await import('@/stores/chat')
      const chatStore = useChatStore()
      chatStore.initializeSignalR()

      return response
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    // 断开SignalR连接
    await signalRService.disconnect()
    
    // 清除状态
    user.value = null
    token.value = null
    
    // 清除localStorage
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  // 更新用户信息
  const updateUser = (userData: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      localStorage.setItem('user', JSON.stringify(user.value))
    }
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    
    // 方法
    initialize,
    login,
    register,
    logout,
    updateUser
  }
})
