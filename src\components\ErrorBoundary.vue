<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-content">
      <div class="error-icon">⚠️</div>
      <h3 class="error-title">出现了一些问题</h3>
      <p class="error-message">{{ errorMessage }}</p>
      <div class="error-actions">
        <van-button type="primary" @click="retry" size="small">
          重试
        </van-button>
        <van-button @click="goHome" size="small">
          返回首页
        </van-button>
      </div>
    </div>
  </div>
  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()
const hasError = ref(false)
const errorMessage = ref('')

// 捕获子组件错误
onErrorCaptured((error: Error, instance, info) => {
  console.error('组件错误:', error)
  console.error('错误信息:', info)
  console.error('组件实例:', instance)
  
  hasError.value = true
  errorMessage.value = error.message || '未知错误'
  
  // 上报错误（可选）
  reportError(error, info)
  
  // 返回false阻止错误继续传播
  return false
})

// 重试
const retry = () => {
  hasError.value = false
  errorMessage.value = ''
  // 可以在这里添加重新加载逻辑
}

// 返回首页
const goHome = () => {
  hasError.value = false
  errorMessage.value = ''
  router.push('/')
}

// 错误上报
const reportError = (error: Error, info: string) => {
  try {
    // 这里可以添加错误上报逻辑
    console.log('错误上报:', {
      message: error.message,
      stack: error.stack,
      info,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    })
  } catch (reportError) {
    console.error('错误上报失败:', reportError)
  }
}
</script>

<style scoped>
.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 20px;
}

.error-content {
  text-align: center;
  max-width: 300px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-title {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 8px;
}

.error-message {
  font-size: 14px;
  color: #646566;
  margin-bottom: 20px;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}
</style>
