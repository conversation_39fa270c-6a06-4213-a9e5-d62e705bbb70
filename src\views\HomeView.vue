<template>
  <div class="home-container">
    <van-nav-bar title="聊天" fixed placeholder>
      <template #right>
        <div class="nav-actions">
          <van-icon
            v-if="isDev"
            name="setting-o"
            size="16"
            @click="goToTest"
            class="test-icon"
            title="测试套件"
          />
          <van-icon name="plus" size="18" @click="showAddMenu = true" />
        </div>
      </template>
    </van-nav-bar>

    <!-- 聊天列表 -->
    <div class="chat-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list>
          <div v-if="chatSessions.length === 0" class="empty-state">
            <van-empty description="暂无聊天记录">
              <van-button type="primary" size="small" @click="goToContacts">
                添加好友开始聊天
              </van-button>
            </van-empty>
          </div>

          <van-swipe-cell
            v-for="session in chatSessions"
            :key="session.id"
          >
            <van-cell
              :title="session.name"
              :label="formatLastMessage(session.lastMessage)"
              :value="formatTime(session.lastMessage?.createdAt)"
              is-link
              @click="openChat(session)"
              class="chat-item"
            >
              <template #icon>
                <div class="avatar-container">
                  <van-image
                    :src="session.avatar || defaultAvatar"
                    round
                    width="40"
                    height="40"
                    fit="cover"
                  />
                  <div
                    v-if="session.type === 'private' && session.isOnline"
                    class="online-indicator"
                  />
                </div>
              </template>

              <template #right-icon>
                <div class="chat-meta">
                  <van-badge
                    v-if="session.unreadCount > 0"
                    :content="session.unreadCount > 99 ? '99+' : session.unreadCount"
                    class="unread-badge"
                  />
                </div>
              </template>
            </van-cell>

            <template #right>
              <van-button
                square
                type="danger"
                text="删除"
                class="delete-button"
                @click="deleteChat(session)"
              />
            </template>
          </van-swipe-cell>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 底部导航 -->
    <van-tabbar v-model="activeTab" fixed placeholder>
      <van-tabbar-item icon="chat-o" name="chat">聊天</van-tabbar-item>
      <van-tabbar-item icon="friends-o" name="contacts" @click="goToContacts">
        联系人
        <van-badge
          v-if="pendingRequestsCount > 0"
          :content="pendingRequestsCount"
          dot
        />
      </van-tabbar-item>
      <van-tabbar-item icon="setting-o" name="profile" @click="goToProfile">我的</van-tabbar-item>
    </van-tabbar>

    <!-- 添加菜单 -->
    <van-action-sheet
      v-model:show="showAddMenu"
      :actions="addActions"
      @select="onAddMenuSelect"
      cancel-text="取消"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showDialog } from 'vant'
import { useChatStore } from '@/stores/chat'
import { useFriendsStore } from '@/stores/friends'
import { useAuthStore } from '@/stores/auth'
import type { ChatSession } from '@/stores/chat'

const router = useRouter()
const chatStore = useChatStore()
const friendsStore = useFriendsStore()
const authStore = useAuthStore()

const activeTab = ref('chat')
const refreshing = ref(false)
const showAddMenu = ref(false)
const isDev = process.env.NODE_ENV === 'development'

const defaultAvatar = 'https://img.yzcdn.cn/vant/cat.jpeg'

const chatSessions = computed(() => chatStore.chatSessions)
const pendingRequestsCount = computed(() => friendsStore.pendingRequestsCount)

const addActions = [
  { name: '添加好友', icon: 'add-o' },
  { name: '创建群聊', icon: 'chat-o' }
]

// 格式化最后一条消息
const formatLastMessage = (message: any) => {
  if (!message) return '暂无消息'

  if (message.type === 'group') {
    return `${message.senderName}: ${message.content}`
  }

  return message.content
}

// 格式化时间
const formatTime = (timeStr: string | undefined) => {
  if (!timeStr) return ''

  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()

  // 今天
  if (diff < 24 * 60 * 60 * 1000 && now.getDate() === time.getDate()) {
    return time.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  }

  // 昨天
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
  if (yesterday.getDate() === time.getDate()) {
    return '昨天'
  }

  // 更早
  return time.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

// 打开聊天
const openChat = (session: ChatSession) => {
  console.log('打开聊天:', session)
  try {
    chatStore.setCurrentChat(session.id)
    const chatPath = `/chat/${session.id}?type=${session.type || 'private'}`
    console.log('跳转到:', chatPath)
    router.push(chatPath)
  } catch (error) {
    console.error('打开聊天失败:', error)
    showToast.fail('打开聊天失败')
  }
}

// 删除聊天会话（微信风格）
const deleteChat = async (session: ChatSession) => {
  try {
    await showDialog({
      title: '删除聊天',
      message: `确定要删除与"${session.name}"的聊天记录吗？`,
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      confirmButtonColor: '#ee0a24'
    })

    chatStore.deleteChatSession(session.id)
    showToast.success('已删除聊天记录')
  } catch (error) {
    // 用户取消删除
    console.log('用户取消删除')
  }
}

// 刷新
const onRefresh = async () => {
  try {
    await friendsStore.fetchFriends()
    await friendsStore.fetchFriendRequests()
  } catch (error) {
    console.error('刷新失败:', error)
  } finally {
    refreshing.value = false
  }
}

// 导航
const goToContacts = () => {
  router.push('/contacts')
}

const goToTest = () => {
  router.push('/test')
}

const goToProfile = () => {
  router.push('/profile')
}

// 添加菜单选择
const onAddMenuSelect = (action: any) => {
  showAddMenu.value = false

  if (action.name === '添加好友') {
    router.push('/contacts/search')
  } else if (action.name === '创建群聊') {
    // TODO: 实现创建群聊功能
    console.log('创建群聊功能待实现')
  }
}

onMounted(async () => {
  // 初始化聊天store（包括加载本地会话和SignalR连接）
  await chatStore.initializeChatStore()

  // 获取好友列表和请求
  await onRefresh()
})
</script>

<style scoped>
.home-container {
  height: 100vh;
  background-color: #f7f8fa;
}

.chat-list {
  padding-bottom: 50px;
}

.chat-item {
  background: white;
  margin-bottom: 1px;
}

.avatar-container {
  position: relative;
  margin-right: 12px;
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #07c160;
  border: 2px solid white;
  border-radius: 50%;
}

.chat-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.unread-badge {
  margin-top: 4px;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 删除按钮样式 */
.delete-button {
  height: 100%;
  border-radius: 0;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.test-icon {
  opacity: 0.7;
  transition: opacity 0.2s ease;
  cursor: pointer;
}

.test-icon:hover {
  opacity: 1;
}

@media (max-width: 480px) {
  .chat-item {
    padding: 12px 16px;
  }

  .nav-actions {
    gap: 8px;
  }
}
</style>
