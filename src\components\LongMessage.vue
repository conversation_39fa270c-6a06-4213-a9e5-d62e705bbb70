<template>
  <div class="long-message-container">
    <div 
      :class="[
        'message-text',
        { 'expanded': isExpanded, 'collapsible': isLongMessage }
      ]"
    >
      <!-- 显示内容 -->
      <div class="message-content" v-html="displayContent"></div>
      
      <!-- 展开/收起按钮 -->
      <div v-if="isLongMessage" class="message-actions">
        <button 
          @click="toggleExpanded"
          class="expand-button wechat-interactive"
          :class="{ 'collapse': isExpanded }"
        >
          {{ isExpanded ? '收起' : '展开全文' }}
          <van-icon 
            :name="isExpanded ? 'arrow-up' : 'arrow-down'" 
            size="12"
            class="expand-icon"
          />
        </button>
      </div>
    </div>
    
    <!-- 长消息预览遮罩 -->
    <div 
      v-if="isLongMessage && !isExpanded" 
      class="message-fade-mask"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Props {
  content: string
  maxLines?: number
  maxLength?: number
  enableHtml?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  maxLines: 5,
  maxLength: 200,
  enableHtml: false
})

const emit = defineEmits<{
  expand: [expanded: boolean]
}>()

const isExpanded = ref(false)

// 检查是否为长消息
const isLongMessage = computed(() => {
  const lineCount = props.content.split('\n').length
  const charCount = props.content.length
  return lineCount > props.maxLines || charCount > props.maxLength
})

// 显示的内容
const displayContent = computed(() => {
  if (!isLongMessage.value || isExpanded.value) {
    return props.enableHtml ? props.content : escapeHtml(props.content)
  }
  
  // 截取内容
  let truncated = props.content
  
  // 按行数截取
  const lines = props.content.split('\n')
  if (lines.length > props.maxLines) {
    truncated = lines.slice(0, props.maxLines).join('\n')
  }
  
  // 按字符数截取
  if (truncated.length > props.maxLength) {
    truncated = truncated.substring(0, props.maxLength)
    // 避免在单词中间截断
    const lastSpace = truncated.lastIndexOf(' ')
    if (lastSpace > props.maxLength * 0.8) {
      truncated = truncated.substring(0, lastSpace)
    }
  }
  
  return props.enableHtml ? truncated : escapeHtml(truncated)
})

// HTML转义
const escapeHtml = (text: string): string => {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML.replace(/\n/g, '<br>')
}

// 切换展开状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
  emit('expand', isExpanded.value)
}

// 监听展开状态变化
watch(isExpanded, (newValue) => {
  if (newValue) {
    // 展开时滚动到消息位置
    setTimeout(() => {
      const container = document.querySelector('.long-message-container')
      if (container) {
        container.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'nearest' 
        })
      }
    }, 100)
  }
})
</script>

<style scoped>
.long-message-container {
  position: relative;
  width: 100%;
}

.message-text {
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-word;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-text.collapsible:not(.expanded) {
  max-height: calc(1.4em * 5); /* 5行的高度 */
  overflow: hidden;
  position: relative;
}

.message-content {
  white-space: pre-wrap;
}

.message-actions {
  margin-top: 8px;
  display: flex;
  justify-content: flex-start;
}

.expand-button {
  background: none;
  border: none;
  color: var(--wechat-primary);
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.expand-button:hover {
  background: rgba(7, 193, 96, 0.1);
  transform: translateY(-1px);
}

.expand-button:active {
  transform: translateY(0);
}

.expand-icon {
  transition: transform 0.2s ease;
}

.expand-button.collapse .expand-icon {
  transform: rotate(180deg);
}

.message-fade-mask {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    var(--wechat-surface) 70%,
    var(--wechat-surface) 100%
  );
  pointer-events: none;
}

/* 自己的消息气泡中的遮罩 */
.own-message .message-fade-mask {
  background: linear-gradient(
    to bottom,
    transparent 0%,
    var(--wechat-primary-light) 70%,
    var(--wechat-primary-light) 100%
  );
}

/* 动画效果 */
.message-text.expanded {
  animation: expandMessage 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes expandMessage {
  from {
    max-height: calc(1.4em * 5);
    opacity: 0.8;
  }
  to {
    max-height: none;
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .expand-button {
    font-size: 12px;
    padding: 2px 6px;
  }
  
  .message-fade-mask {
    height: 30px;
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .message-fade-mask {
    background: linear-gradient(
      to bottom,
      transparent 0%,
      var(--wechat-surface) 70%,
      var(--wechat-surface) 100%
    );
  }
  
  .own-message .message-fade-mask {
    background: linear-gradient(
      to bottom,
      transparent 0%,
      var(--wechat-primary-light) 70%,
      var(--wechat-primary-light) 100%
    );
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .expand-button {
    border: 1px solid var(--wechat-primary);
  }
  
  .message-fade-mask {
    border-top: 1px solid var(--wechat-border);
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .message-text,
  .expand-button,
  .expand-icon {
    transition: none;
  }
  
  .message-text.expanded {
    animation: none;
  }
}
</style>
