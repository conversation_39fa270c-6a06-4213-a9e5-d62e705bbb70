<template>
  <div class="emoji-picker" v-show="visible">
    <div class="emoji-header">
      <div class="emoji-tabs">
        <div 
          v-for="(category, index) in emojiCategories" 
          :key="index"
          class="emoji-tab"
          :class="{ active: activeTab === index }"
          @click="activeTab = index"
        >
          {{ category.icon }}
        </div>
      </div>
      <div class="emoji-close" @click="$emit('close')">
        <van-icon name="cross" />
      </div>
    </div>
    
    <div class="emoji-content">
      <div class="emoji-grid">
        <div 
          v-for="emoji in currentEmojis" 
          :key="emoji.code"
          class="emoji-item"
          @click="selectEmoji(emoji)"
        >
          {{ emoji.emoji }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue'

interface Emoji {
  emoji: string
  code: string
  name: string
}

interface EmojiCategory {
  name: string
  icon: string
  emojis: Emoji[]
}

defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  close: []
  select: [emoji: string]
}>()

const activeTab = ref(0)

// Emoji数据
const emojiCategories: EmojiCategory[] = [
  {
    name: '表情',
    icon: '😀',
    emojis: [
      { emoji: '😀', code: ':grinning:', name: '开心' },
      { emoji: '😃', code: ':smiley:', name: '笑脸' },
      { emoji: '😄', code: ':smile:', name: '大笑' },
      { emoji: '😁', code: ':grin:', name: '露齿笑' },
      { emoji: '😆', code: ':laughing:', name: '哈哈' },
      { emoji: '😅', code: ':sweat_smile:', name: '汗笑' },
      { emoji: '🤣', code: ':rofl:', name: '笑哭' },
      { emoji: '😂', code: ':joy:', name: '喜极而泣' },
      { emoji: '🙂', code: ':slightly_smiling_face:', name: '微笑' },
      { emoji: '🙃', code: ':upside_down_face:', name: '倒脸' },
      { emoji: '😉', code: ':wink:', name: '眨眼' },
      { emoji: '😊', code: ':blush:', name: '害羞' },
      { emoji: '😇', code: ':innocent:', name: '天使' },
      { emoji: '🥰', code: ':smiling_face_with_hearts:', name: '爱心眼' },
      { emoji: '😍', code: ':heart_eyes:', name: '花痴' },
      { emoji: '🤩', code: ':star_struck:', name: '星星眼' },
      { emoji: '😘', code: ':kissing_heart:', name: '飞吻' },
      { emoji: '😗', code: ':kissing:', name: '亲吻' },
      { emoji: '😚', code: ':kissing_smiling_eyes:', name: '闭眼亲吻' },
      { emoji: '😙', code: ':kissing_closed_eyes:', name: '亲吻微笑' },
      { emoji: '🥲', code: ':smiling_face_with_tear:', name: '含泪微笑' },
      { emoji: '😋', code: ':yum:', name: '美味' },
      { emoji: '😛', code: ':stuck_out_tongue:', name: '吐舌' },
      { emoji: '😜', code: ':stuck_out_tongue_winking_eye:', name: '调皮' },
      { emoji: '🤪', code: ':zany_face:', name: '疯狂' },
      { emoji: '😝', code: ':stuck_out_tongue_closed_eyes:', name: '吐舌闭眼' },
      { emoji: '🤑', code: ':money_mouth_face:', name: '财迷' },
      { emoji: '🤗', code: ':hugs:', name: '拥抱' },
      { emoji: '🤭', code: ':hand_over_mouth:', name: '捂嘴' },
      { emoji: '🤫', code: ':shushing_face:', name: '嘘' },
      { emoji: '🤔', code: ':thinking:', name: '思考' },
      { emoji: '🤐', code: ':zipper_mouth_face:', name: '闭嘴' },
      { emoji: '🤨', code: ':raised_eyebrow:', name: '挑眉' },
      { emoji: '😐', code: ':neutral_face:', name: '面无表情' },
      { emoji: '😑', code: ':expressionless:', name: '无语' },
      { emoji: '😶', code: ':no_mouth:', name: '无嘴' },
      { emoji: '😏', code: ':smirk:', name: '得意' },
      { emoji: '😒', code: ':unamused:', name: '不爽' },
      { emoji: '🙄', code: ':roll_eyes:', name: '翻白眼' },
      { emoji: '😬', code: ':grimacing:', name: '龇牙' },
      { emoji: '🤥', code: ':lying_face:', name: '说谎' },
      { emoji: '😔', code: ':pensive:', name: '沉思' },
      { emoji: '😪', code: ':sleepy:', name: '困' },
      { emoji: '🤤', code: ':drooling_face:', name: '流口水' },
      { emoji: '😴', code: ':sleeping:', name: '睡觉' },
      { emoji: '😷', code: ':mask:', name: '口罩' },
      { emoji: '🤒', code: ':face_with_thermometer:', name: '发烧' },
      { emoji: '🤕', code: ':face_with_head_bandage:', name: '受伤' },
      { emoji: '🤢', code: ':nauseated_face:', name: '恶心' },
      { emoji: '🤮', code: ':vomiting_face:', name: '呕吐' },
      { emoji: '🤧', code: ':sneezing_face:', name: '打喷嚏' },
      { emoji: '🥵', code: ':hot_face:', name: '热' },
      { emoji: '🥶', code: ':cold_face:', name: '冷' },
      { emoji: '🥴', code: ':woozy_face:', name: '晕' },
      { emoji: '😵', code: ':dizzy_face:', name: '眩晕' },
      { emoji: '🤯', code: ':exploding_head:', name: '爆炸' },
      { emoji: '🤠', code: ':cowboy_hat_face:', name: '牛仔' },
      { emoji: '🥳', code: ':partying_face:', name: '派对' },
      { emoji: '🥸', code: ':disguised_face:', name: '伪装' },
      { emoji: '😎', code: ':sunglasses:', name: '墨镜' },
      { emoji: '🤓', code: ':nerd_face:', name: '书呆子' },
      { emoji: '🧐', code: ':monocle_face:', name: '单片眼镜' }
    ]
  },
  {
    name: '手势',
    icon: '👋',
    emojis: [
      { emoji: '👋', code: ':wave:', name: '挥手' },
      { emoji: '🤚', code: ':raised_back_of_hand:', name: '举手背' },
      { emoji: '🖐️', code: ':raised_hand_with_fingers_splayed:', name: '张开手' },
      { emoji: '✋', code: ':raised_hand:', name: '举手' },
      { emoji: '🖖', code: ':vulcan_salute:', name: '瓦肯礼' },
      { emoji: '👌', code: ':ok_hand:', name: 'OK' },
      { emoji: '🤌', code: ':pinched_fingers:', name: '捏手指' },
      { emoji: '🤏', code: ':pinching_hand:', name: '捏' },
      { emoji: '✌️', code: ':victory_hand:', name: '胜利' },
      { emoji: '🤞', code: ':crossed_fingers:', name: '交叉手指' },
      { emoji: '🤟', code: ':love_you_gesture:', name: '爱你手势' },
      { emoji: '🤘', code: ':metal:', name: '摇滚' },
      { emoji: '🤙', code: ':call_me_hand:', name: '打电话' },
      { emoji: '👈', code: ':point_left:', name: '左指' },
      { emoji: '👉', code: ':point_right:', name: '右指' },
      { emoji: '👆', code: ':point_up_2:', name: '上指' },
      { emoji: '🖕', code: ':middle_finger:', name: '中指' },
      { emoji: '👇', code: ':point_down:', name: '下指' },
      { emoji: '☝️', code: ':point_up:', name: '食指向上' },
      { emoji: '👍', code: ':thumbsup:', name: '赞' },
      { emoji: '👎', code: ':thumbsdown:', name: '踩' },
      { emoji: '✊', code: ':fist:', name: '拳头' },
      { emoji: '👊', code: ':facepunch:', name: '出拳' },
      { emoji: '🤛', code: ':left_facing_fist:', name: '左拳' },
      { emoji: '🤜', code: ':right_facing_fist:', name: '右拳' },
      { emoji: '👏', code: ':clap:', name: '鼓掌' },
      { emoji: '🙌', code: ':raised_hands:', name: '举双手' },
      { emoji: '👐', code: ':open_hands:', name: '张开双手' },
      { emoji: '🤲', code: ':palms_up_together:', name: '合掌' },
      { emoji: '🤝', code: ':handshake:', name: '握手' },
      { emoji: '🙏', code: ':pray:', name: '祈祷' }
    ]
  },
  {
    name: '爱心',
    icon: '❤️',
    emojis: [
      { emoji: '❤️', code: ':heart:', name: '红心' },
      { emoji: '🧡', code: ':orange_heart:', name: '橙心' },
      { emoji: '💛', code: ':yellow_heart:', name: '黄心' },
      { emoji: '💚', code: ':green_heart:', name: '绿心' },
      { emoji: '💙', code: ':blue_heart:', name: '蓝心' },
      { emoji: '💜', code: ':purple_heart:', name: '紫心' },
      { emoji: '🖤', code: ':black_heart:', name: '黑心' },
      { emoji: '🤍', code: ':white_heart:', name: '白心' },
      { emoji: '🤎', code: ':brown_heart:', name: '棕心' },
      { emoji: '💔', code: ':broken_heart:', name: '心碎' },
      { emoji: '❣️', code: ':heavy_heart_exclamation:', name: '心叹号' },
      { emoji: '💕', code: ':two_hearts:', name: '双心' },
      { emoji: '💞', code: ':revolving_hearts:', name: '旋转心' },
      { emoji: '💓', code: ':heartbeat:', name: '心跳' },
      { emoji: '💗', code: ':heartpulse:', name: '心脉冲' },
      { emoji: '💖', code: ':sparkling_heart:', name: '闪亮心' },
      { emoji: '💘', code: ':cupid:', name: '丘比特' },
      { emoji: '💝', code: ':gift_heart:', name: '礼物心' },
      { emoji: '💟', code: ':heart_decoration:', name: '心装饰' }
    ]
  },
  {
    name: '动物',
    icon: '🐶',
    emojis: [
      { emoji: '🐶', code: ':dog:', name: '狗' },
      { emoji: '🐱', code: ':cat:', name: '猫' },
      { emoji: '🐭', code: ':mouse:', name: '鼠' },
      { emoji: '🐹', code: ':hamster:', name: '仓鼠' },
      { emoji: '🐰', code: ':rabbit:', name: '兔子' },
      { emoji: '🦊', code: ':fox_face:', name: '狐狸' },
      { emoji: '🐻', code: ':bear:', name: '熊' },
      { emoji: '🐼', code: ':panda_face:', name: '熊猫' },
      { emoji: '🐨', code: ':koala:', name: '考拉' },
      { emoji: '🐯', code: ':tiger:', name: '老虎' },
      { emoji: '🦁', code: ':lion:', name: '狮子' },
      { emoji: '🐮', code: ':cow:', name: '牛' },
      { emoji: '🐷', code: ':pig:', name: '猪' },
      { emoji: '🐸', code: ':frog:', name: '青蛙' },
      { emoji: '🐵', code: ':monkey_face:', name: '猴子' },
      { emoji: '🙈', code: ':see_no_evil:', name: '非礼勿视' },
      { emoji: '🙉', code: ':hear_no_evil:', name: '非礼勿听' },
      { emoji: '🙊', code: ':speak_no_evil:', name: '非礼勿言' },
      { emoji: '🐒', code: ':monkey:', name: '猴' },
      { emoji: '🐔', code: ':chicken:', name: '鸡' },
      { emoji: '🐧', code: ':penguin:', name: '企鹅' },
      { emoji: '🐦', code: ':bird:', name: '鸟' },
      { emoji: '🐤', code: ':baby_chick:', name: '小鸡' },
      { emoji: '🐣', code: ':hatching_chick:', name: '破壳小鸡' },
      { emoji: '🐥', code: ':hatched_chick:', name: '小黄鸡' },
      { emoji: '🦆', code: ':duck:', name: '鸭子' },
      { emoji: '🦅', code: ':eagle:', name: '鹰' },
      { emoji: '🦉', code: ':owl:', name: '猫头鹰' },
      { emoji: '🦇', code: ':bat:', name: '蝙蝠' },
      { emoji: '🐺', code: ':wolf:', name: '狼' },
      { emoji: '🐗', code: ':boar:', name: '野猪' },
      { emoji: '🐴', code: ':horse:', name: '马' },
      { emoji: '🦄', code: ':unicorn:', name: '独角兽' },
      { emoji: '🐝', code: ':bee:', name: '蜜蜂' },
      { emoji: '🐛', code: ':bug:', name: '虫子' },
      { emoji: '🦋', code: ':butterfly:', name: '蝴蝶' },
      { emoji: '🐌', code: ':snail:', name: '蜗牛' },
      { emoji: '🐞', code: ':beetle:', name: '甲虫' },
      { emoji: '🐜', code: ':ant:', name: '蚂蚁' },
      { emoji: '🦟', code: ':mosquito:', name: '蚊子' },
      { emoji: '🦗', code: ':cricket:', name: '蟋蟀' },
      { emoji: '🕷️', code: ':spider:', name: '蜘蛛' },
      { emoji: '🕸️', code: ':spider_web:', name: '蜘蛛网' },
      { emoji: '🦂', code: ':scorpion:', name: '蝎子' }
    ]
  },
  {
    name: '食物',
    icon: '🍎',
    emojis: [
      { emoji: '🍎', code: ':apple:', name: '苹果' },
      { emoji: '🍊', code: ':tangerine:', name: '橘子' },
      { emoji: '🍋', code: ':lemon:', name: '柠檬' },
      { emoji: '🍌', code: ':banana:', name: '香蕉' },
      { emoji: '🍉', code: ':watermelon:', name: '西瓜' },
      { emoji: '🍇', code: ':grapes:', name: '葡萄' },
      { emoji: '🍓', code: ':strawberry:', name: '草莓' },
      { emoji: '🫐', code: ':blueberries:', name: '蓝莓' },
      { emoji: '🍈', code: ':melon:', name: '甜瓜' },
      { emoji: '🍒', code: ':cherries:', name: '樱桃' },
      { emoji: '🍑', code: ':peach:', name: '桃子' },
      { emoji: '🥭', code: ':mango:', name: '芒果' },
      { emoji: '🍍', code: ':pineapple:', name: '菠萝' },
      { emoji: '🥥', code: ':coconut:', name: '椰子' },
      { emoji: '🥝', code: ':kiwi_fruit:', name: '猕猴桃' },
      { emoji: '🍅', code: ':tomato:', name: '番茄' },
      { emoji: '🍆', code: ':eggplant:', name: '茄子' },
      { emoji: '🥑', code: ':avocado:', name: '牛油果' },
      { emoji: '🥦', code: ':broccoli:', name: '西兰花' },
      { emoji: '🥬', code: ':leafy_greens:', name: '绿叶菜' },
      { emoji: '🥒', code: ':cucumber:', name: '黄瓜' },
      { emoji: '🌶️', code: ':hot_pepper:', name: '辣椒' },
      { emoji: '🫑', code: ':bell_pepper:', name: '甜椒' },
      { emoji: '🌽', code: ':corn:', name: '玉米' },
      { emoji: '🥕', code: ':carrot:', name: '胡萝卜' },
      { emoji: '🫒', code: ':olive:', name: '橄榄' },
      { emoji: '🧄', code: ':garlic:', name: '大蒜' },
      { emoji: '🧅', code: ':onion:', name: '洋葱' },
      { emoji: '🥔', code: ':potato:', name: '土豆' },
      { emoji: '🍠', code: ':sweet_potato:', name: '红薯' },
      { emoji: '🥐', code: ':croissant:', name: '牛角包' },
      { emoji: '🥖', code: ':baguette_bread:', name: '法棍' },
      { emoji: '🍞', code: ':bread:', name: '面包' },
      { emoji: '🥨', code: ':pretzel:', name: '椒盐脆饼' },
      { emoji: '🥯', code: ':bagel:', name: '百吉饼' },
      { emoji: '🥞', code: ':pancakes:', name: '煎饼' },
      { emoji: '🧇', code: ':waffle:', name: '华夫饼' },
      { emoji: '🧀', code: ':cheese:', name: '奶酪' },
      { emoji: '🍖', code: ':meat_on_bone:', name: '带骨肉' },
      { emoji: '🍗', code: ':poultry_leg:', name: '鸡腿' },
      { emoji: '🥩', code: ':cut_of_meat:', name: '肉排' },
      { emoji: '🥓', code: ':bacon:', name: '培根' },
      { emoji: '🍔', code: ':hamburger:', name: '汉堡' },
      { emoji: '🍟', code: ':fries:', name: '薯条' },
      { emoji: '🍕', code: ':pizza:', name: '披萨' },
      { emoji: '🌭', code: ':hotdog:', name: '热狗' },
      { emoji: '🥪', code: ':sandwich:', name: '三明治' },
      { emoji: '🌮', code: ':taco:', name: '玉米饼' },
      { emoji: '🌯', code: ':burrito:', name: '卷饼' },
      { emoji: '🫔', code: ':tamale:', name: '粽子' },
      { emoji: '🥙', code: ':stuffed_flatbread:', name: '夹饼' },
      { emoji: '🧆', code: ':falafel:', name: '沙拉三明治' },
      { emoji: '🥚', code: ':egg:', name: '鸡蛋' },
      { emoji: '🍳', code: ':fried_egg:', name: '煎蛋' },
      { emoji: '🥘', code: ':shallow_pan_of_food:', name: '浅锅食物' },
      { emoji: '🍲', code: ':stew:', name: '炖菜' },
      { emoji: '🫕', code: ':fondue:', name: '火锅' },
      { emoji: '🥣', code: ':bowl_with_spoon:', name: '碗和勺' },
      { emoji: '🥗', code: ':green_salad:', name: '沙拉' },
      { emoji: '🍿', code: ':popcorn:', name: '爆米花' },
      { emoji: '🧈', code: ':butter:', name: '黄油' },
      { emoji: '🧂', code: ':salt:', name: '盐' },
      { emoji: '🥫', code: ':canned_food:', name: '罐头' }
    ]
  }
]

const currentEmojis = computed(() => {
  return emojiCategories[activeTab.value]?.emojis || []
})

// 防抖处理，避免连续快速点击
let selectTimer: NodeJS.Timeout | null = null

const selectEmoji = (emoji: Emoji) => {
  try {
    // 清除之前的定时器
    if (selectTimer) {
      clearTimeout(selectTimer)
    }

    // 防抖处理，避免连续快速选择
    selectTimer = setTimeout(() => {
      try {
        emit('select', emoji.emoji)
      } catch (emitError) {
        console.error('发送表情选择事件失败:', emitError)
      }
    }, 50) // 50ms防抖

  } catch (error) {
    console.error('选择表情失败:', error)
    // 即使出错也尝试发送事件
    try {
      emit('select', emoji.emoji)
    } catch (fallbackError) {
      console.error('表情选择完全失败:', fallbackError)
    }
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (selectTimer) {
    clearTimeout(selectTimer)
    selectTimer = null
  }
})
</script>

<style scoped>
.emoji-picker {
  position: absolute;
  bottom: 60px;
  left: 0;
  right: 0;
  height: 300px;
  background: var(--theme-surface);
  border-top: 1px solid var(--theme-border);
  border-radius: 12px 12px 0 0;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.emoji-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--theme-border);
  background: var(--theme-surface);
}

.emoji-tabs {
  display: flex;
  gap: 16px;
}

.emoji-tab {
  font-size: 20px;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.emoji-tab:hover {
  background: var(--theme-border);
}

.emoji-tab.active {
  background: var(--theme-primary);
  color: white;
}

.emoji-close {
  padding: 8px;
  border-radius: 50%;
  cursor: pointer;
  color: var(--theme-text-secondary);
  transition: all 0.2s;
}

.emoji-close:hover {
  background: var(--theme-border);
  color: var(--theme-text);
}

.emoji-content {
  height: calc(100% - 60px);
  overflow-y: auto;
  padding: 16px;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
}

.emoji-item {
  font-size: 24px;
  padding: 8px;
  text-align: center;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  user-select: none;
}

.emoji-item:hover {
  background: var(--theme-border);
  transform: scale(1.2);
}

.emoji-item:active {
  transform: scale(1.1);
}

/* 移动端优化 */
@media (max-width: 768px) {
  .emoji-picker {
    height: 280px;
  }
  
  .emoji-grid {
    grid-template-columns: repeat(7, 1fr);
    gap: 6px;
  }
  
  .emoji-item {
    font-size: 20px;
    padding: 6px;
  }
  
  .emoji-tabs {
    gap: 12px;
  }
  
  .emoji-tab {
    font-size: 18px;
    padding: 6px;
  }
}

/* 滚动条样式 */
.emoji-content::-webkit-scrollbar {
  width: 4px;
}

.emoji-content::-webkit-scrollbar-track {
  background: transparent;
}

.emoji-content::-webkit-scrollbar-thumb {
  background: var(--theme-border);
  border-radius: 2px;
}

.emoji-content::-webkit-scrollbar-thumb:hover {
  background: var(--theme-text-secondary);
}
</style>
