import * as signalR from '@microsoft/signalr'

class SignalRService {
  private connection: signalR.HubConnection | null = null
  private isConnected = false

  // 初始化连接
  async initialize(token: string) {
    if (this.connection) {
      await this.disconnect()
    }

    this.connection = new signalR.HubConnectionBuilder()
      .withUrl('http://localhost:5057/chatHub', {
        accessTokenFactory: () => token,
        transport: signalR.HttpTransportType.WebSockets
      })
      .withAutomaticReconnect()
      .build()

    // 设置事件监听器
    this.setupEventListeners()

    try {
      await this.connection.start()
      this.isConnected = true
      console.log('SignalR连接成功')
    } catch (error) {
      console.error('SignalR连接失败:', error)
      throw error
    }
  }

  // 设置事件监听器
  private setupEventListeners() {
    if (!this.connection) return

    // 连接状态变化
    this.connection.onreconnecting(() => {
      console.log('SignalR重连中...')
      this.isConnected = false
    })

    this.connection.onreconnected(() => {
      console.log('SignalR重连成功')
      this.isConnected = true
    })

    this.connection.onclose(() => {
      console.log('SignalR连接关闭')
      this.isConnected = false
    })
  }

  // 监听消息
  onReceiveMessage(callback: (message: any) => void) {
    if (this.connection) {
      this.connection.on('ReceiveMessage', callback)
    }
  }

  // 监听消息发送确认
  onMessageSent(callback: (message: any) => void) {
    if (this.connection) {
      this.connection.on('MessageSent', callback)
    }
  }

  // 监听好友状态变化
  onFriendStatusChanged(callback: (data: { userId: number; isOnline: boolean }) => void) {
    if (this.connection) {
      this.connection.on('FriendStatusChanged', callback)
    }
  }

  // 监听错误
  onError(callback: (error: string) => void) {
    if (this.connection) {
      this.connection.on('Error', callback)
    }
  }

  // 发送私聊消息
  async sendPrivateMessage(receiverId: number, content: string) {
    if (this.connection && this.isConnected) {
      try {
        await this.connection.invoke('SendPrivateMessage', receiverId, content)
      } catch (error) {
        console.error('发送私聊消息失败:', error)
        throw error
      }
    } else {
      throw new Error('SignalR连接未建立')
    }
  }

  // 发送群聊消息
  async sendGroupMessage(groupId: number, content: string) {
    if (this.connection && this.isConnected) {
      try {
        await this.connection.invoke('SendGroupMessage', groupId, content)
      } catch (error) {
        console.error('发送群聊消息失败:', error)
        throw error
      }
    } else {
      throw new Error('SignalR连接未建立')
    }
  }

  // 加入群聊
  async joinGroup(groupId: number) {
    if (this.connection && this.isConnected) {
      try {
        await this.connection.invoke('JoinGroup', groupId)
      } catch (error) {
        console.error('加入群聊失败:', error)
        throw error
      }
    }
  }

  // 离开群聊
  async leaveGroup(groupId: number) {
    if (this.connection && this.isConnected) {
      try {
        await this.connection.invoke('LeaveGroup', groupId)
      } catch (error) {
        console.error('离开群聊失败:', error)
        throw error
      }
    }
  }

  // 断开连接
  async disconnect() {
    if (this.connection) {
      try {
        await this.connection.stop()
        this.isConnected = false
        console.log('SignalR连接已断开')
      } catch (error) {
        console.error('断开SignalR连接失败:', error)
      }
    }
  }

  // 获取连接状态
  getConnectionState() {
    return this.isConnected
  }
}

// 导出单例实例
export const signalRService = new SignalRService()
export default signalRService
