import * as signalR from '@microsoft/signalr'

class SignalRService {
  private connection: signalR.HubConnection | null = null
  private isConnected = false

  // 初始化连接
  async initialize(token: string, retryCount = 0) {
    if (this.connection) {
      await this.disconnect()
    }

    // 使用相对路径，通过Vite代理转发
    const hubUrl = import.meta.env.DEV
      ? '/chatHub'
      : `${import.meta.env.VITE_BACKEND_URL || 'http://localhost:5057'}/chatHub`

    console.log('SignalR连接URL:', hubUrl)

    this.connection = new signalR.HubConnectionBuilder()
      .withUrl(hubUrl, {
        accessTokenFactory: () => {
          console.log('SignalR请求token:', token ? '已提供' : '未提供')
          return token
        },
        transport: signalR.HttpTransportType.WebSockets | signalR.HttpTransportType.LongPolling,
        skipNegotiation: false, // 允许协商传输方式
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      .withAutomaticReconnect([0, 2000, 10000, 30000]) // 自定义重连间隔
      .configureLogging(signalR.LogLevel.Information) // 增加日志输出以便调试
      .build()

    // 设置事件监听器
    this.setupEventListeners()

    try {
      await this.connection.start()
      this.isConnected = true
      console.log('SignalR连接成功')
    } catch (error) {
      console.error('SignalR连接失败:', error)

      // 如果是开发环境且后端可能未启动，提供友好提示
      if (import.meta.env.DEV && retryCount < 3) {
        console.log(`SignalR连接重试 ${retryCount + 1}/3...`)
        await new Promise(resolve => setTimeout(resolve, 2000))
        return this.initialize(token, retryCount + 1)
      }

      // 不抛出错误，允许应用继续运行（离线模式）
      console.warn('SignalR连接失败，应用将在离线模式下运行')
    }
  }

  // 设置事件监听器
  private setupEventListeners() {
    if (!this.connection) return

    // 连接状态变化
    this.connection.onreconnecting(() => {
      console.log('SignalR重连中...')
      this.isConnected = false
    })

    this.connection.onreconnected(() => {
      console.log('SignalR重连成功')
      this.isConnected = true
    })

    this.connection.onclose(() => {
      console.log('SignalR连接关闭')
      this.isConnected = false
    })
  }

  // 监听消息
  onReceiveMessage(callback: (message: any) => void) {
    if (this.connection) {
      this.connection.on('ReceiveMessage', callback)
    }
  }

  // 监听消息发送确认
  onMessageSent(callback: (message: any) => void) {
    if (this.connection) {
      this.connection.on('MessageSent', callback)
    }
  }

  // 监听好友状态变化
  onFriendStatusChanged(callback: (data: { userId: number; isOnline: boolean }) => void) {
    if (this.connection) {
      this.connection.on('FriendStatusChanged', callback)
    }
  }

  // 监听错误
  onError(callback: (error: string) => void) {
    if (this.connection) {
      this.connection.on('Error', callback)
    }
  }

  // 发送私聊消息
  async sendPrivateMessage(receiverId: number, content: string) {
    if (this.connection && this.isConnected) {
      try {
        await this.connection.invoke('SendPrivateMessage', receiverId, content)
      } catch (error) {
        console.error('发送私聊消息失败:', error)
        throw error
      }
    } else {
      throw new Error('SignalR连接未建立')
    }
  }

  // 发送群聊消息
  async sendGroupMessage(groupId: number, content: string) {
    if (this.connection && this.isConnected) {
      try {
        await this.connection.invoke('SendGroupMessage', groupId, content)
      } catch (error) {
        console.error('发送群聊消息失败:', error)
        throw error
      }
    } else {
      throw new Error('SignalR连接未建立')
    }
  }

  // 加入群聊
  async joinGroup(groupId: number) {
    if (this.connection && this.isConnected) {
      try {
        await this.connection.invoke('JoinGroup', groupId)
      } catch (error) {
        console.error('加入群聊失败:', error)
        throw error
      }
    }
  }

  // 离开群聊
  async leaveGroup(groupId: number) {
    if (this.connection && this.isConnected) {
      try {
        await this.connection.invoke('LeaveGroup', groupId)
      } catch (error) {
        console.error('离开群聊失败:', error)
        throw error
      }
    }
  }

  // 断开连接
  async disconnect() {
    if (this.connection) {
      try {
        await this.connection.stop()
        this.isConnected = false
        console.log('SignalR连接已断开')
      } catch (error) {
        console.error('断开SignalR连接失败:', error)
      }
    }
  }

  // 获取连接状态
  getConnectionState() {
    return this.isConnected
  }

  // 获取详细连接状态
  getDetailedConnectionState() {
    if (!this.connection) {
      return { state: 'Disconnected', isConnected: false, message: '连接未初始化' }
    }

    const state = this.connection.state
    return {
      state: signalR.HubConnectionState[state],
      isConnected: this.isConnected,
      message: this.getStateMessage(state)
    }
  }

  private getStateMessage(state: signalR.HubConnectionState): string {
    switch (state) {
      case signalR.HubConnectionState.Disconnected:
        return '已断开连接'
      case signalR.HubConnectionState.Connecting:
        return '正在连接...'
      case signalR.HubConnectionState.Connected:
        return '已连接'
      case signalR.HubConnectionState.Disconnecting:
        return '正在断开连接...'
      case signalR.HubConnectionState.Reconnecting:
        return '正在重连...'
      default:
        return '未知状态'
    }
  }
}

// 导出单例实例
export const signalRService = new SignalRService()
export default signalRService
