<template>
  <div class="contacts-container">
    <van-nav-bar title="联系人" fixed placeholder>
      <template #right>
        <van-icon name="search" size="18" @click="goToSearch" />
      </template>
    </van-nav-bar>

    <div class="contacts-content">
      <!-- 功能菜单 -->
      <van-cell-group>
        <van-cell
          title="新的朋友"
          icon="add-o"
          is-link
          @click="goToFriendRequests"
        >
          <template #right-icon>
            <van-badge
              v-if="pendingRequestsCount > 0"
              :content="pendingRequestsCount"
              class="request-badge"
            />
          </template>
        </van-cell>
        <van-cell title="群聊" icon="chat-o" is-link @click="goToGroups" />
      </van-cell-group>

      <!-- 调试按钮 -->
      <div class="debug-section" style="padding: 16px;">
        <van-button
          type="primary"
          size="small"
          @click="testChatNavigation"
          style="margin-bottom: 8px; margin-right: 8px;"
        >
          测试聊天跳转
        </van-button>
        <van-button
          type="success"
          size="small"
          @click="forceNavigateToChat"
          style="margin-bottom: 8px;"
        >
          强制跳转聊天
        </van-button>
      </div>

      <!-- 好友列表 -->
      <div class="friends-section">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <!-- 在线好友 -->
          <div v-if="onlineFriends.length > 0">
            <van-divider content-position="left">
              在线好友 ({{ onlineFriends.length }})
            </van-divider>
            
            <van-cell-group>
              <van-cell
                v-for="friend in onlineFriends"
                :key="friend.id"
                :title="friend.displayName"
                :label="friend.username"
                is-link
                @click="startChat(friend)"
              >
                <template #icon>
                  <div class="avatar-container">
                    <van-image
                      :src="friend.avatar || defaultAvatar"
                      round
                      width="40"
                      height="40"
                      fit="cover"
                    />
                    <div class="online-indicator" />
                  </div>
                </template>
              </van-cell>
            </van-cell-group>
          </div>

          <!-- 离线好友 -->
          <div v-if="offlineFriends.length > 0">
            <van-divider content-position="left">
              离线好友 ({{ offlineFriends.length }})
            </van-divider>
            
            <van-cell-group>
              <van-cell
                v-for="friend in offlineFriends"
                :key="friend.id"
                :title="friend.displayName"
                :label="`最后在线: ${formatLastSeen(friend.lastSeen)}`"
                is-link
                @click="startChat(friend)"
              >
                <template #icon>
                  <div class="avatar-container">
                    <van-image
                      :src="friend.avatar || defaultAvatar"
                      round
                      width="40"
                      height="40"
                      fit="cover"
                    />
                  </div>
                </template>
              </van-cell>
            </van-cell-group>
          </div>

          <!-- 空状态 -->
          <div v-if="friends.length === 0 && !isLoading" class="empty-state">
            <van-empty description="暂无好友">
              <van-button type="primary" size="small" @click="goToSearch">
                添加好友
              </van-button>
            </van-empty>
          </div>
        </van-pull-refresh>
      </div>
    </div>

    <!-- 底部导航 -->
    <van-tabbar v-model="activeTab" fixed placeholder>
      <van-tabbar-item icon="chat-o" name="chat" @click="goToHome">聊天</van-tabbar-item>
      <van-tabbar-item icon="friends-o" name="contacts">联系人</van-tabbar-item>
      <van-tabbar-item icon="setting-o" name="profile" @click="goToProfile">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useFriendsStore } from '@/stores/friends'
import { useChatStore } from '@/stores/chat'
import type { Friend } from '@/stores/friends'

const router = useRouter()
const friendsStore = useFriendsStore()
const chatStore = useChatStore()

const activeTab = ref('contacts')
const refreshing = ref(false)
const defaultAvatar = 'https://img.yzcdn.cn/vant/cat.jpeg'

const friends = computed(() => friendsStore.friends)
const onlineFriends = computed(() => friendsStore.onlineFriends)
const offlineFriends = computed(() => friendsStore.offlineFriends)
const pendingRequestsCount = computed(() => friendsStore.pendingRequestsCount)
const isLoading = computed(() => friendsStore.isLoading)

// 格式化最后在线时间
const formatLastSeen = (timeStr: string) => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return time.toLocaleDateString('zh-CN')
}

// 测试聊天导航
const testChatNavigation = async () => {
  console.log('测试聊天导航开始')
  console.log('当前路由:', router.currentRoute.value.path)
  console.log('好友数据:', friends.value)

  try {
    // 创建一个测试聊天会话
    const testSession = {
      id: '999',
      type: 'private' as const,
      name: '测试聊天',
      avatar: '',
      unreadCount: 0,
      isOnline: true
    }

    console.log('添加测试聊天会话:', testSession)
    chatStore.addChatSession(testSession)

    const testPath = '/chat/999?type=private'
    console.log('准备跳转到:', testPath)

    await router.push(testPath)
    console.log('跳转完成，当前路由:', router.currentRoute.value.path)

    showToast({ type: 'success', message: '测试跳转成功' })
  } catch (error) {
    console.error('测试跳转失败:', error)
    showToast({ type: 'fail', message: '测试跳转失败' })
  }
}

// 强制跳转到聊天页面
const forceNavigateToChat = () => {
  console.log('强制跳转开始')
  console.log('当前路由:', router.currentRoute.value.fullPath)

  // 直接使用window.location跳转
  const chatUrl = '/chat/2?type=private'
  console.log('强制跳转到:', chatUrl)

  window.location.href = chatUrl
}

// 开始聊天
const startChat = async (friend: Friend) => {
  console.log('开始与好友聊天:', friend)
  console.log('当前路由:', router.currentRoute.value.path)

  try {
    // 添加聊天会话
    const chatSession = {
      id: friend.id.toString(),
      type: 'private' as const,
      name: friend.displayName,
      avatar: friend.avatar,
      unreadCount: 0,
      isOnline: friend.isOnline
    }

    console.log('添加聊天会话:', chatSession)
    chatStore.addChatSession(chatSession)

    // 跳转到聊天页面
    const chatPath = `/chat/${friend.id}?type=private`
    console.log('准备跳转到:', chatPath)

    await router.push(chatPath)
    console.log('跳转完成，当前路由:', router.currentRoute.value.path)
  } catch (error) {
    console.error('开始聊天失败:', error)
    showToast({ type: 'fail', message: '开始聊天失败' })
  }
}

// 刷新
const onRefresh = async () => {
  try {
    await friendsStore.fetchFriends()
    await friendsStore.fetchFriendRequests()
  } catch (error) {
    console.error('刷新失败:', error)
  } finally {
    refreshing.value = false
  }
}

// 导航
const goToHome = () => {
  router.push('/')
}

const goToProfile = () => {
  router.push('/profile')
}

const goToSearch = () => {
  router.push('/contacts/search')
}

const goToFriendRequests = () => {
  router.push('/contacts/requests')
}

const goToGroups = () => {
  router.push('/groups')
}

onMounted(async () => {
  await onRefresh()
})
</script>

<style scoped>
.contacts-container {
  height: 100vh;
  background-color: #f7f8fa;
}

.contacts-content {
  padding-bottom: 50px;
}

.friends-section {
  margin-top: 8px;
}

.avatar-container {
  position: relative;
  margin-right: 12px;
}

.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #07c160;
  border: 2px solid white;
  border-radius: 50%;
}

.request-badge {
  margin-right: 8px;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

@media (max-width: 480px) {
  .contacts-content {
    padding: 0;
  }
}
</style>
