import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { friendsAPI } from '@/services/api'

export interface Friend {
  id: number
  username: string
  displayName: string
  avatar?: string
  isOnline: boolean
  lastSeen: string
}

export interface FriendRequest {
  id: number
  requester: {
    id: number
    username: string
    displayName: string
    avatar?: string
  }
  createdAt: string
}

export interface SearchUser {
  id: number
  username: string
  displayName: string
  avatar?: string
  friendshipStatus?: string
}

export const useFriendsStore = defineStore('friends', () => {
  // 状态
  const friends = ref<Friend[]>([])
  const friendRequests = ref<FriendRequest[]>([])
  const searchResults = ref<SearchUser[]>([])
  const isLoading = ref(false)
  const isSearching = ref(false)

  // 计算属性
  const onlineFriends = computed(() => friends.value.filter(friend => friend.isOnline))
  const offlineFriends = computed(() => friends.value.filter(friend => !friend.isOnline))
  const pendingRequestsCount = computed(() => friendRequests.value.length)

  // 获取好友列表
  const fetchFriends = async () => {
    isLoading.value = true
    try {
      const response = await friendsAPI.getFriends()
      friends.value = response
    } catch (error) {
      console.error('获取好友列表失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 获取好友请求
  const fetchFriendRequests = async () => {
    try {
      const response = await friendsAPI.getPendingRequests()
      friendRequests.value = response
    } catch (error) {
      console.error('获取好友请求失败:', error)
      throw error
    }
  }

  // 搜索用户
  const searchUsers = async (query: string) => {
    if (!query.trim()) {
      searchResults.value = []
      return
    }

    isSearching.value = true
    try {
      const response = await friendsAPI.searchUsers(query)
      searchResults.value = response
    } catch (error) {
      console.error('搜索用户失败:', error)
      throw error
    } finally {
      isSearching.value = false
    }
  }

  // 发送好友请求
  const sendFriendRequest = async (userId: number) => {
    try {
      await friendsAPI.sendFriendRequest(userId)
      
      // 更新搜索结果中的状态
      const user = searchResults.value.find(u => u.id === userId)
      if (user) {
        user.friendshipStatus = 'Pending'
      }
    } catch (error) {
      console.error('发送好友请求失败:', error)
      throw error
    }
  }

  // 接受好友请求
  const acceptFriendRequest = async (requesterId: number) => {
    try {
      await friendsAPI.acceptFriendRequest(requesterId)
      
      // 从请求列表中移除
      friendRequests.value = friendRequests.value.filter(req => req.requester.id !== requesterId)
      
      // 重新获取好友列表
      await fetchFriends()
    } catch (error) {
      console.error('接受好友请求失败:', error)
      throw error
    }
  }

  // 拒绝好友请求
  const rejectFriendRequest = async (requesterId: number) => {
    try {
      await friendsAPI.rejectFriendRequest(requesterId)
      
      // 从请求列表中移除
      friendRequests.value = friendRequests.value.filter(req => req.requester.id !== requesterId)
    } catch (error) {
      console.error('拒绝好友请求失败:', error)
      throw error
    }
  }

  // 拉黑用户
  const blockUser = async (userId: number) => {
    try {
      await friendsAPI.blockUser(userId)
      
      // 从好友列表中移除
      friends.value = friends.value.filter(friend => friend.id !== userId)
    } catch (error) {
      console.error('拉黑用户失败:', error)
      throw error
    }
  }

  // 解除拉黑
  const unblockUser = async (userId: number) => {
    try {
      await friendsAPI.unblockUser(userId)
    } catch (error) {
      console.error('解除拉黑失败:', error)
      throw error
    }
  }

  // 删除好友
  const removeFriend = async (userId: number) => {
    try {
      await friendsAPI.removeFriend(userId)
      
      // 从好友列表中移除
      friends.value = friends.value.filter(friend => friend.id !== userId)
    } catch (error) {
      console.error('删除好友失败:', error)
      throw error
    }
  }

  // 更新好友在线状态
  const updateFriendStatus = (userId: number, isOnline: boolean) => {
    const friend = friends.value.find(f => f.id === userId)
    if (friend) {
      friend.isOnline = isOnline
      if (!isOnline) {
        friend.lastSeen = new Date().toISOString()
      }
    }
  }

  // 清除搜索结果
  const clearSearchResults = () => {
    searchResults.value = []
  }

  // 清除所有数据
  const clearAll = () => {
    friends.value = []
    friendRequests.value = []
    searchResults.value = []
  }

  return {
    // 状态
    friends,
    friendRequests,
    searchResults,
    isLoading,
    isSearching,
    
    // 计算属性
    onlineFriends,
    offlineFriends,
    pendingRequestsCount,
    
    // 方法
    fetchFriends,
    fetchFriendRequests,
    searchUsers,
    sendFriendRequest,
    acceptFriendRequest,
    rejectFriendRequest,
    blockUser,
    unblockUser,
    removeFriend,
    updateFriendStatus,
    clearSearchResults,
    clearAll
  }
})
