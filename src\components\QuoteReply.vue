<template>
  <div class="quote-reply-container">
    <!-- 引用的原消息 -->
    <div class="quoted-message" @click="scrollToOriginal">
      <div class="quote-line"></div>
      <div class="quote-content">
        <div class="quote-sender">{{ quotedMessage.senderName }}</div>
        <div class="quote-text">{{ formatQuoteContent(quotedMessage.content) }}</div>
      </div>
    </div>
    
    <!-- 回复内容 -->
    <div class="reply-content">
      <LongMessage 
        :content="replyContent"
        :max-lines="5"
        :max-length="200"
        @expand="$emit('expand', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import LongMessage from './LongMessage.vue'
import type { MessageWithStatus } from '@/utils/messageStatus'

interface Props {
  quotedMessage: MessageWithStatus
  replyContent: string
  isOwnMessage?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isOwnMessage: false
})

const emit = defineEmits<{
  expand: [expanded: boolean]
  scrollToOriginal: [messageId: string | number]
}>()

// 格式化引用内容
const formatQuoteContent = (content: string): string => {
  const maxLength = 60
  if (content.length <= maxLength) {
    return content
  }
  return content.substring(0, maxLength) + '...'
}

// 滚动到原消息
const scrollToOriginal = () => {
  emit('scrollToOriginal', props.quotedMessage.id)
}
</script>

<style scoped>
.quote-reply-container {
  width: 100%;
}

.quoted-message {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.quoted-message:hover {
  background: rgba(0, 0, 0, 0.08);
}

.quoted-message:active {
  background: rgba(0, 0, 0, 0.1);
}

.quote-line {
  width: 3px;
  height: 100%;
  background: var(--wechat-primary);
  border-radius: 2px;
  flex-shrink: 0;
  min-height: 30px;
}

.quote-content {
  flex: 1;
  min-width: 0;
}

.quote-sender {
  font-size: 12px;
  color: var(--wechat-primary);
  font-weight: 500;
  margin-bottom: 2px;
  line-height: 1.2;
}

.quote-text {
  font-size: 13px;
  color: var(--wechat-text-secondary);
  line-height: 1.3;
  word-wrap: break-word;
  word-break: break-word;
}

.reply-content {
  width: 100%;
}

/* 自己的消息中的引用样式 */
.own-message .quoted-message {
  background: rgba(255, 255, 255, 0.2);
}

.own-message .quoted-message:hover {
  background: rgba(255, 255, 255, 0.3);
}

.own-message .quoted-message:active {
  background: rgba(255, 255, 255, 0.4);
}

.own-message .quote-text {
  color: rgba(0, 0, 0, 0.7);
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .quoted-message {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .quoted-message:hover {
    background: rgba(255, 255, 255, 0.15);
  }
  
  .quoted-message:active {
    background: rgba(255, 255, 255, 0.2);
  }
  
  .own-message .quoted-message {
    background: rgba(0, 0, 0, 0.2);
  }
  
  .own-message .quoted-message:hover {
    background: rgba(0, 0, 0, 0.3);
  }
  
  .own-message .quoted-message:active {
    background: rgba(0, 0, 0, 0.4);
  }
  
  .own-message .quote-text {
    color: rgba(255, 255, 255, 0.7);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .quoted-message {
    padding: 6px 10px;
    margin-bottom: 6px;
  }
  
  .quote-sender {
    font-size: 11px;
  }
  
  .quote-text {
    font-size: 12px;
  }
}

/* 动画效果 */
.quoted-message {
  animation: slideInQuote 0.3s ease-out;
}

@keyframes slideInQuote {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .quoted-message {
    border: 1px solid var(--wechat-border);
  }
  
  .quote-line {
    background: var(--wechat-primary);
    width: 4px;
  }
  
  .quote-sender {
    font-weight: 600;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .quoted-message {
    animation: none;
    transition: none;
  }
}
</style>
