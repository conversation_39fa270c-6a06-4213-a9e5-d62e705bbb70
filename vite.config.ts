import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'https://localhost:7250', // 使用HTTPS端口
        changeOrigin: true,
        secure: false, // 忽略SSL证书验证（开发环境）
        timeout: 10000,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, res) => {
            console.log('API Proxy error:', err.message);
            // 如果后端不可用，返回友好的错误信息
            if (!res.headersSent) {
              res.writeHead(503, {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
              });
              res.end(JSON.stringify({
                error: 'Backend service unavailable',
                message: '后端服务暂时不可用，请检查后端服务器是否启动',
                code: 'BACKEND_UNAVAILABLE'
              }));
            }
          });
          proxy.on('proxyReq', (_proxyReq, req, _res) => {
            console.log('Sending API Request:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received API Response:', proxyRes.statusCode, req.url);
          });
        },
      },
      // SignalR WebSocket代理
      '/chatHub': {
        target: 'https://localhost:7250', // 使用HTTPS端口
        changeOrigin: true,
        secure: false, // 忽略SSL证书验证（开发环境）
        ws: true, // 启用WebSocket代理
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('SignalR Proxy error:', err.message);
          });
          proxy.on('proxyReqWs', (_proxyReq, req, _socket) => {
            console.log('SignalR WebSocket Request:', req.url);
          });
        },
      }
    }
  }
})
